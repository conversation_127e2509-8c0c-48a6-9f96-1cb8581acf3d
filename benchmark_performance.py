#!/usr/bin/env python3
"""
PCT模型性能基准测试脚本
测量GFLOPs和FPS性能指标
"""

import os
import sys
import time
import argparse
import torch
import torch.nn as nn
import numpy as np
from mmcv import Config
from mmcv.runner import load_checkpoint
from mmcv.cnn import get_model_complexity_info

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))
from models import build_posenet

def parse_args():
    parser = argparse.ArgumentParser(description='PCT Performance Benchmark')
    parser.add_argument('config', help='config file path')
    parser.add_argument('checkpoint', help='checkpoint file path')
    parser.add_argument('--input-shape', 
                       type=int, 
                       nargs='+', 
                       default=[3, 256, 256],
                       help='input tensor shape (C H W)')
    parser.add_argument('--batch-size', 
                       type=int, 
                       default=1,
                       help='batch size for FPS testing')
    parser.add_argument('--num-warmup', 
                       type=int, 
                       default=10,
                       help='number of warmup iterations')
    parser.add_argument('--num-test', 
                       type=int, 
                       default=100,
                       help='number of test iterations for FPS')
    parser.add_argument('--device', 
                       type=str, 
                       default='cuda:0',
                       help='device to run benchmark')
    return parser.parse_args()

def measure_gflops(model, input_shape):
    """测量模型的GFLOPs"""
    print("🔍 测量GFLOPs...")

    # 使用mmcv的复杂度分析工具
    try:
        # 创建一个简化的模型包装器，只处理图像输入
        class ModelWrapper(nn.Module):
            def __init__(self, original_model):
                super().__init__()
                self.model = original_model

            def forward(self, x):
                # 创建虚拟的其他输入
                batch_size = x.size(0)
                num_joints = 17
                joints_3d = torch.randn(batch_size, num_joints, 3).to(x.device)
                joints_3d_visible = torch.ones(batch_size, num_joints, 1).to(x.device)
                img_metas = [{'image_file': 'dummy.jpg', 'center': [128, 128],
                             'scale': [1.0, 1.0], 'rotation': 0, 'bbox_score': 1.0,
                             'flip_pairs': []} for _ in range(batch_size)]

                return self.model(x, joints_3d=joints_3d,
                                joints_3d_visible=joints_3d_visible,
                                img_metas=img_metas, return_loss=False)

        wrapped_model = ModelWrapper(model)

        flops, params = get_model_complexity_info(
            wrapped_model,
            input_shape,
            print_per_layer_stat=False,
            as_strings=False
        )

        # 转换为GFLOPs
        gflops = flops / 1e9
        params_m = params / 1e6

        print(f"✅ GFLOPs: {gflops:.2f}")
        print(f"✅ Parameters: {params_m:.2f}M")

        return gflops, params_m

    except Exception as e:
        print(f"❌ GFLOPs测量失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def measure_fps(model, input_shape, batch_size, num_warmup, num_test, device):
    """测量模型的FPS"""
    print(f"🔍 测量FPS (batch_size={batch_size})...")
    
    model.eval()
    
    # 创建随机输入
    input_tensor = torch.randn(batch_size, *input_shape).to(device)
    
    # 创建虚拟的关键点数据
    num_joints = 17  # COCO数据集
    joints_3d = torch.randn(batch_size, num_joints, 3).to(device)
    joints_3d_visible = torch.ones(batch_size, num_joints, 1).to(device)
    
    # 创建img_metas
    img_metas = []
    for i in range(batch_size):
        img_metas.append({
            'image_file': f'dummy_{i}.jpg',
            'center': np.array([128, 128]),
            'scale': np.array([1.0, 1.0]),
            'rotation': 0,
            'bbox_score': 1.0,
            'flip_pairs': []
        })
    
    # 预热
    print(f"🔥 预热 {num_warmup} 次...")
    with torch.no_grad():
        for _ in range(num_warmup):
            try:
                _ = model(input_tensor, 
                         joints_3d=joints_3d, 
                         joints_3d_visible=joints_3d_visible,
                         img_metas=img_metas, 
                         return_loss=False)
            except Exception as e:
                print(f"❌ 预热失败: {e}")
                return None
    
    # 同步GPU
    if device.type == 'cuda':
        torch.cuda.synchronize()
    
    # 测量FPS
    print(f"⏱️ 测量 {num_test} 次推理时间...")
    start_time = time.time()
    
    with torch.no_grad():
        for _ in range(num_test):
            try:
                _ = model(input_tensor, 
                         joints_3d=joints_3d, 
                         joints_3d_visible=joints_3d_visible,
                         img_metas=img_metas, 
                         return_loss=False)
            except Exception as e:
                print(f"❌ FPS测量失败: {e}")
                return None
    
    # 同步GPU
    if device.type == 'cuda':
        torch.cuda.synchronize()
    
    end_time = time.time()
    
    # 计算FPS
    total_time = end_time - start_time
    avg_time_per_batch = total_time / num_test
    avg_time_per_image = avg_time_per_batch / batch_size
    fps = 1.0 / avg_time_per_image
    
    print(f"✅ 平均每批次时间: {avg_time_per_batch*1000:.2f}ms")
    print(f"✅ 平均每张图像时间: {avg_time_per_image*1000:.2f}ms")
    print(f"✅ FPS: {fps:.2f}")
    
    return fps, avg_time_per_image

def main():
    args = parse_args()
    
    print("🚀 PCT模型性能基准测试")
    print("=" * 50)
    print(f"配置文件: {args.config}")
    print(f"权重文件: {args.checkpoint}")
    print(f"输入形状: {args.input_shape}")
    print(f"批量大小: {args.batch_size}")
    print(f"设备: {args.device}")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        return
    
    if not os.path.exists(args.checkpoint):
        print(f"❌ 权重文件不存在: {args.checkpoint}")
        return
    
    # 设置设备
    device = torch.device(args.device)
    if device.type == 'cuda' and not torch.cuda.is_available():
        print("❌ CUDA不可用，切换到CPU")
        device = torch.device('cpu')
        args.device = 'cpu'
    
    # 加载配置
    cfg = Config.fromfile(args.config)
    
    # 构建模型
    print("🔧 构建模型...")
    model = build_posenet(cfg.model)
    
    # 加载权重
    print("📦 加载权重...")
    load_checkpoint(model, args.checkpoint, map_location='cpu')
    
    # 移动到指定设备
    model = model.to(device)
    model.eval()
    
    print("✅ 模型准备完成")
    print()
    
    # 测量GFLOPs
    gflops, params = measure_gflops(model, tuple(args.input_shape))
    print()
    
    # 测量FPS
    fps, avg_time = measure_fps(
        model, 
        args.input_shape, 
        args.batch_size, 
        args.num_warmup, 
        args.num_test, 
        device
    )
    print()
    
    # 输出总结
    print("📊 性能测试结果总结")
    print("=" * 50)
    if gflops is not None:
        print(f"GFLOPs: {gflops:.2f}")
    if params is not None:
        print(f"Parameters: {params:.2f}M")
    if fps is not None:
        print(f"FPS: {fps:.2f}")
        print(f"平均推理时间: {avg_time*1000:.2f}ms")
    print("=" * 50)

if __name__ == '__main__':
    main()
