import torch
import torch.nn as nn
import torch.nn.functional as F
from mmpose.models.builder import LOSSES

@LOSSES.register_module()
class KLDivergenceHeatmapLoss(nn.Module):
    """
    KL Divergence loss for heatmaps.
    Assumes model output ('output') are logits and target ('target') are soft probabilities (e.g., Gaussian heatmaps).
    Calculates KL(target_softmax || output_softmax) for each joint heatmap.
    """
    def __init__(self, use_target_weight=True, reduction='mean', epsilon=1e-8):
        super().__init__()
        self.use_target_weight = use_target_weight
        self.epsilon = epsilon
        # KLDivLoss expects input as log-probabilities and target as probabilities.
        # reduction='none' allows custom weighting and averaging.
        self.kl_loss = nn.KLDivLoss(reduction='none', log_target=False)
        self.reduction_str = reduction

    def forward(self, output, target, target_weight=None):
        """
        Args:
            output (torch.Tensor): Predicted heatmaps (logits) from the model.
                                   Shape (B, K, H, W).
            target (torch.Tensor): Target heatmaps (e.g., Gaussian distributions).
                                   Shape (B, K, H, W).
            target_weight (torch.Tensor, optional): Weight of each target joint.
                                                    Shape (B, K, 1, 1) or (B, K).
        Returns:
            torch.Tensor: The calculated KL divergence loss.
        """
        if output.shape != target.shape:
            raise ValueError(
                f"Output shape {output.shape} does not match target shape {target.shape}"
            )

        batch_size, num_joints, height, width = output.shape

        # Reshape for applying softmax/log_softmax spatially for each joint
        # (B, K, H, W) -> (B*K, H*W)
        output_flat = output.view(batch_size * num_joints, -1)
        target_flat = target.view(batch_size * num_joints, -1)

        # 1. Convert model output (logits) to log-probabilities
        log_pred_probs = F.log_softmax(output_flat, dim=-1) # Shape: (B*K, H*W)

        # 2. Convert target heatmaps to probabilities (normalize to sum to 1)
        #    This is crucial for KLDivLoss.
        target_probs = target_flat / (target_flat.sum(dim=-1, keepdim=True) + self.epsilon) # Shape: (B*K, H*W)

        # 3. Calculate KL divergence
        #    nn.KLDivLoss computes: sum(target_probs * (log(target_probs) - log_pred_probs))
        #    However, if log_target=False (which we set), it computes: sum(target_probs * (-log_pred_probs))
        #    This is not exactly KL(target||pred).
        #    For KL(P||Q) = sum(P * log(P/Q)), we need P=target_probs, Q=pred_probs (softmax of output)
        #    So, log_pred_probs is log(Q). We need log(P) as well.
        #    Let's use the direct formula: sum(P * (logP - logQ))
        
        log_target_probs = torch.log(target_probs + self.epsilon) # Add epsilon for stability

        # Element-wise KL divergence: target_probs * (log_target_probs - log_pred_probs)
        kl_elements = target_probs * (log_target_probs - log_pred_probs) # Shape: (B*K, H*W)
        
        # Sum over the spatial dimension (H*W) for each joint
        loss_per_joint = kl_elements.sum(dim=-1) # Shape: (B*K)
        loss = loss_per_joint.view(batch_size, num_joints) # Shape: (B, K)

        # 4. Apply target_weight
        if self.use_target_weight and target_weight is not None:
            if target_weight.dim() == 4: # (B, K, 1, 1)
                target_weight_squeezed = target_weight.squeeze(-1).squeeze(-1) # (B, K)
            elif target_weight.dim() == 2: # (B, K)
                target_weight_squeezed = target_weight
            elif target_weight.dim() == 3 and target_weight.shape[-1] == 1: # (B, K, 1) <--- 新增处理
                target_weight_squeezed = target_weight.squeeze(-1) # (B, K)
            else:
                raise ValueError(f"Unsupported target_weight shape: {target_weight.shape}")
            loss = loss * target_weight_squeezed

        # 5. Final reduction
        if self.reduction_str == 'mean':
            if self.use_target_weight and target_weight is not None:
                # Average over valid (weighted) joints
                valid_joints_count = torch.sum(target_weight_squeezed > 0)
                if valid_joints_count > 0:
                    return loss.sum() / valid_joints_count
                else: # No valid joints
                    return torch.tensor(0.0, device=output.device, requires_grad=True)
            else: # No target_weight, mean over all B*K elements
                return loss.mean()
        elif self.reduction_str == 'sum':
            return loss.sum()
        else:
            raise ValueError(f"Unsupported reduction: {self.reduction_str}")
