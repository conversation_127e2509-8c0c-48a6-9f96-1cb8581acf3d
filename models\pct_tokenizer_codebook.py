# --------------------------------------------------------
# Pose Compositional Tokens
# Written by <PERSON><PERSON><PERSON> (<EMAIL>)
# --------------------------------------------------------

import os
import torch
import torch.nn as nn
import torch.nn.functional as F
# import torch.distributed as dist

from mmpose.models.builder import build_loss
from mmpose.models.builder import HEADS
from timm.models.layers import trunc_normal_

from .modules import MixerLayer
from .estimate_invisible_points import InvisiblePointEstimator

@HEADS.register_module()
class GroupedL2Loss(nn.Module):
    def __init__(self, head_indices, arm_indices, leg_indices, arm_weight=2.0, leg_weight=2.0):
        super().__init__()
        self.head_indices = head_indices
        self.arm_indices = arm_indices
        self.leg_indices = leg_indices
        self.arm_weight = arm_weight
        self.leg_weight = leg_weight

    def forward(self, pred, gt):
        # 计算头和躯干关节点的损失
        # 提取可见点信息
        visible = gt[:, :, -1].unsqueeze(-1)

         # 计算头和躯干关节点的损失
        head_loss = torch.sqrt(F.mse_loss(pred[:, self.head_indices, :], gt[:, self.head_indices, :-1], reduction='none') + 1e-6)
        head_loss_visible = (head_loss * visible[:, self.head_indices, :]).mean()

        # 计算手臂关节点的损失
        arm_loss = torch.sqrt(F.mse_loss(pred[:, self.arm_indices, :], gt[:, self.arm_indices, :-1], reduction='none') + 1e-6)
        arm_loss_visible = (arm_loss * visible[:, self.arm_indices, :]).mean() * self.arm_weight

        # 计算腿部关节点的损失
        leg_loss = torch.sqrt(F.mse_loss(pred[:, self.leg_indices, :], gt[:, self.leg_indices, :-1], reduction='none') + 1e-6)
        leg_loss_visible = (leg_loss * visible[:, self.leg_indices, :]).mean() * self.leg_weight

        # 组合损失
        reg_loss = head_loss_visible + arm_loss_visible + leg_loss_visible

        return reg_loss
    
class SELayer(nn.Module):
    def __init__(self, channel, reduction=16):
        super(SELayer, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channel, channel // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channel // reduction, channel, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)

class PCT_Tokenizer(nn.Module):
    """ Tokenizer of Pose Compositional Tokens.
        paper ref: Zigang Geng et al. "Human Pose as
            Compositional Tokens"

    Args:
        stage_pct (str): Training stage (Tokenizer or Classifier).
        tokenizer (list): Config about the tokenizer.
        num_joints (int): Number of annotated joints in the dataset.
        guide_ratio (float): The ratio of image guidance.
        guide_channels (int): Feature Dim of the image guidance.
    """

    def __init__(self,
                 stage_pct,
                 tokenizer=None,
                 num_joints=17,
                 guide_ratio=0,
                 guide_channels=0):
        super().__init__()

        self.stage_pct = stage_pct
        self.guide_ratio = guide_ratio
        self.num_joints = num_joints
        self.invisible_estimator = InvisiblePointEstimator(min_visible_for_estimation=3)

        # 定义关节分组
        self.head_indices = [0, 1, 2, 3, 4]  # 鼻子，左眼，右眼，左耳，右耳
        self.torso_indices = [5, 6, 11, 12]  # 左肩，右肩，左臀，右臀
        self.mid_limb_indices = [7, 8, 13, 14]  # 左肘，右肘，左膝，右膝
        self.end_limb_indices = [9, 10, 15, 16]  # 左腕，右腕，左踝，右踝


        self.drop_rate = tokenizer['encoder']['drop_rate']     
        self.enc_num_blocks = tokenizer['encoder']['num_blocks']
        self.enc_hidden_dim = tokenizer['encoder']['hidden_dim']
        self.enc_token_inter_dim = tokenizer['encoder']['token_inter_dim']
        self.enc_hidden_inter_dim = tokenizer['encoder']['hidden_inter_dim']
        self.enc_dropout = tokenizer['encoder']['dropout']
        self.mask_noise_scale = tokenizer['encoder'].get('mask_noise_scale', 0.1)

        self.dec_num_blocks = tokenizer['decoder']['num_blocks']
        self.dec_hidden_dim = tokenizer['decoder']['hidden_dim']
        self.dec_token_inter_dim = tokenizer['decoder']['token_inter_dim']
        self.dec_hidden_inter_dim = tokenizer['decoder']['hidden_inter_dim']
        self.dec_dropout = tokenizer['decoder']['dropout']

        self.token_num = tokenizer['codebook']['token_num']
        self.token_class_num = tokenizer['codebook']['token_class_num']
        self.token_dim = tokenizer['codebook']['token_dim']
        self.decay = tokenizer['codebook']['ema_decay']

        # 不再分割 token_num，每组都使用完整的 token_num
        self.tokens_per_group = self.token_num  # 每组都使用完整的 token_num (34)

        self.invisible_token = nn.Parameter(torch.zeros(1, 1, self.enc_hidden_dim))
        trunc_normal_(self.invisible_token, mean=0., std=0.02, a=-0.02, b=0.02)

        if self.guide_ratio > 0:
            self.start_img_embed = nn.Linear(
                guide_channels, int(self.enc_hidden_dim*self.guide_ratio))
        self.start_embed = nn.Linear(
            2, int(self.enc_hidden_dim*(1-self.guide_ratio)))
        
        self.encoder = nn.ModuleList(
            [MixerLayer(self.enc_hidden_dim, self.enc_hidden_inter_dim, 
                self.num_joints, self.enc_token_inter_dim,
                self.enc_dropout) for _ in range(self.enc_num_blocks)])
        self.encoder_layer_norm = nn.LayerNorm(self.enc_hidden_dim)
        
        # 为每个组创建单独的token_mlp和feature_embed
        self.group_token_mlps = nn.ModuleDict({
            'head': nn.Linear(len(self.head_indices), self.token_num),
            'torso': nn.Linear(len(self.torso_indices), self.token_num),
            'mid_limb': nn.Linear(len(self.mid_limb_indices), self.token_num),
            'end_limb': nn.Linear(len(self.end_limb_indices), self.token_num)
        })
        
        self.group_feature_embeds = nn.ModuleDict({
            'head': nn.Linear(self.enc_hidden_dim, self.token_dim),
            'torso': nn.Linear(self.enc_hidden_dim, self.token_dim),
            'mid_limb': nn.Linear(self.enc_hidden_dim, self.token_dim),
            'end_limb': nn.Linear(self.enc_hidden_dim, self.token_dim)
        })
        # 添加合并后的特征映射层
        self.combined_token_projection = nn.Linear(self.token_num * 4, self.token_num)

        # 为每个组创建单独的码本
        for group_name in ['head', 'torso', 'mid_limb', 'end_limb']:
            self.register_buffer(f'codebook_{group_name}', 
                torch.empty(self.token_class_num, self.token_dim))
            getattr(self, f'codebook_{group_name}').data.normal_()
            
            self.register_buffer(f'ema_cluster_size_{group_name}', 
                torch.zeros(self.token_class_num))
            
            self.register_buffer(f'ema_w_{group_name}', 
                torch.empty(self.token_class_num, self.token_dim))
            getattr(self, f'ema_w_{group_name}').data.normal_()

        
        self.token_mlp = nn.Linear(
            self.num_joints, self.token_num)
        self.feature_embed = nn.Linear(
            self.enc_hidden_dim, self.token_dim)

        self.register_buffer('codebook', 
            torch.empty(self.token_class_num, self.token_dim))
        self.codebook.data.normal_()
        self.register_buffer('ema_cluster_size', 
            torch.zeros(self.token_class_num))
        self.register_buffer('ema_w', 
            torch.empty(self.token_class_num, self.token_dim))
        self.ema_w.data.normal_()

        # 针对合并后的token特征的投影层
        self.token_fusion = nn.Linear(self.token_dim * 4, self.token_dim)        
        
        self.decoder_token_mlp = nn.Linear(
            self.token_num, self.num_joints)
        self.decoder_start = nn.Linear(
            self.token_dim, self.dec_hidden_dim)

        self.decoder = nn.ModuleList(
            [MixerLayer(self.dec_hidden_dim, self.dec_hidden_inter_dim,
                self.num_joints, self.dec_token_inter_dim, 
                self.dec_dropout) for _ in range(self.dec_num_blocks)])
        self.decoder_layer_norm = nn.LayerNorm(self.dec_hidden_dim)

        # 初始化新的损失函数
        self.grouped_l2_loss = GroupedL2Loss(
            head_indices=[0, 1, 2, 3, 4],  # 头和躯干关节点索引
            arm_indices=[5, 6, 7, 8, 9, 10],  # 手臂关节点索引
            leg_indices=[11, 12, 13, 14, 15, 16]  # 腿部关节点索引
        )

        self.learnable_positional_encoding = nn.Parameter(
        torch.randn(self.num_joints, 256)
        )
        nn.init.trunc_normal_(self.learnable_positional_encoding, mean=0., std=0.02)
        
        # 添加线性层用于映射拼接后的位置编码
        self.position_encoding_proj = nn.Linear(512, 256)

        self.recover_embed = nn.Linear(self.dec_hidden_dim, 2)
        self.se = SELayer(channel=17)
        self.loss = build_loss(tokenizer['loss_keypoint'])

    def process_group_features(self, encode_feat, group_indices, group_name, bs, device):
        """处理每个组的特征并更新对应码本"""
        # 提取组特征
        group_feat = encode_feat[:, group_indices, :]
        
        # 转置并通过Token MLP
        group_feat = group_feat.transpose(2, 1)
        group_feat = self.group_token_mlps[group_name](group_feat).transpose(2, 1)
        
        # 嵌入到token维度
        group_feat = self.group_feature_embeds[group_name](group_feat).flatten(0, 1)
        
        # 计算与码本的距离
        group_codebook = getattr(self, f'codebook_{group_name}')
        distances = torch.sum(group_feat**2, dim=1, keepdim=True) \
            + torch.sum(group_codebook**2, dim=1) \
            - 2 * torch.matmul(group_feat, group_codebook.t())
            
        # 找到最近的码本向量
        encoding_indices = torch.argmin(distances, dim=1)
        
        # 创建one-hot编码
        encodings = torch.zeros(
            encoding_indices.shape[0], self.token_class_num, device=device)
        encodings.scatter_(1, encoding_indices.unsqueeze(1), 1)
        
        # 通过码本获取特征
        part_token_feat = torch.matmul(encodings, group_codebook)
        
        # 返回结果以及编码信息
        return part_token_feat, encodings, encoding_indices, group_feat
    
    def update_codebook(self, encodings, encode_feat, group_name, train):
        """更新特定组的码本"""
        if not (train and self.stage_pct == "tokenizer"):
            return None
            
        # 计算更新梯度
        dw = torch.matmul(encodings.t(), encode_feat.detach())
        
        # 同步 (如果是分布式训练)
        n_encodings, n_dw = encodings.numel(), dw.numel()
        encodings_shape, dw_shape = encodings.shape, dw.shape
        combined = torch.cat((encodings.flatten(), dw.flatten()))
        # 这里省略了分布式同步
        sync_encodings, sync_dw = torch.split(combined, [n_encodings, n_dw])
        sync_encodings, sync_dw = sync_encodings.view(encodings_shape), sync_dw.view(dw_shape)
        
        # 更新聚类大小
        ema_cluster_size = getattr(self, f'ema_cluster_size_{group_name}')
        ema_cluster_size = ema_cluster_size * self.decay + (1 - self.decay) * torch.sum(sync_encodings, 0)
        
        # 归一化
        n = torch.sum(ema_cluster_size.data)
        ema_cluster_size = ((ema_cluster_size + 1e-5) / (n + self.token_class_num * 1e-5) * n)
        
        # 存回buffer
        self.register_buffer(f'ema_cluster_size_{group_name}', ema_cluster_size)
        
        # 更新累积梯度
        ema_w = getattr(self, f'ema_w_{group_name}')
        ema_w = ema_w * self.decay + (1 - self.decay) * sync_dw
        self.register_buffer(f'ema_w_{group_name}', ema_w)
        
        # 更新码本
        codebook = ema_w / ema_cluster_size.unsqueeze(1)
        self.register_buffer(f'codebook_{group_name}', codebook)
        
        # 在最后一个组处理完后，更新主码本
        if group_name == 'end_limb':
            # 1. 先使用原始方法更新原码本
            # 先生成全身关节的编码和特征
            full_joints_encodings = torch.cat([self.head_encodings, self.torso_encodings, 
                                        self.mid_limb_encodings, self.end_limb_encodings], dim=0)
            full_joints_features = torch.cat([self.head_feat, self.torso_feat, 
                                        self.mid_limb_feat, self.end_limb_feat], dim=0)
            
            # 计算全身关节的更新梯度
            full_dw = torch.matmul(full_joints_encodings.t(), full_joints_features.detach())
            
            # 更新原始码本的聚类大小
            self.ema_cluster_size = self.ema_cluster_size * self.decay + \
                                (1 - self.decay) * torch.sum(full_joints_encodings, 0)
            n = torch.sum(self.ema_cluster_size.data)
            self.ema_cluster_size = ((self.ema_cluster_size + 1e-5) / 
                                (n + self.token_class_num * 1e-5) * n)
            
            # 更新原始码本的权重
            self.ema_w = self.ema_w * self.decay + (1 - self.decay) * full_dw
            
            # 更新原始码本
            original_codebook = self.ema_w / self.ema_cluster_size.unsqueeze(1)
            
            # 2. 构建拼接码本并投影
            concat_books = []
            for i in range(self.token_class_num):
                concat_vector = torch.cat([
                    self.codebook_head[i], 
                    self.codebook_torso[i], 
                    self.codebook_mid_limb[i], 
                    self.codebook_end_limb[i]
                ])
                concat_books.append(concat_vector)
            
            concat_books = torch.stack(concat_books)  # shape: [token_class_num, token_dim*4]
            projected_books = self.token_fusion(concat_books)  # shape: [token_class_num, token_dim]
            
            # 3. 最终更新：原码本与投影码本的加权组合
            alpha = 0.7  # 原码本权重
            beta = 0.3   # 分组投影码本权重
            self.codebook = alpha * original_codebook + beta * projected_books

    def position_encoding(self, joints_coord):
        """Generate position encoding for joints coordinates.

        Args:
            joints_coord (torch.Tensor): The coordinates of joints, shape (bs, num_joints, 2).

        Returns:
            torch.Tensor: Position encoding, shape (bs, num_joints, enc_hidden_dim).
        """
        bs, num_joints, _ = joints_coord.shape
        enc_hidden_dim = 256  # 新的局部变量，值为 256
        position_encoding = torch.zeros((bs, num_joints, enc_hidden_dim), device=joints_coord.device)

        div_term = torch.exp(torch.arange(0, enc_hidden_dim, 2, device=joints_coord.device) * -(torch.log(torch.tensor(10000.0)) / enc_hidden_dim))
        position_encoding[:, :, 0::2] = torch.sin(joints_coord[:, :, 0:1] * div_term)
        position_encoding[:, :, 1::2] = torch.cos(joints_coord[:, :, 0:1] * div_term)

        # 可学习位置编码
        learnable_encoding = self.learnable_positional_encoding.unsqueeze(0).expand(bs, -1, -1)

        # 拼接
        combined_encoding = torch.cat((position_encoding, learnable_encoding), dim=-1)
        # 映射到256维
        combined_encoding = self.position_encoding_proj(combined_encoding)

        return combined_encoding
    
    def forward(self, joints, joints_feature, cls_logits, train=True):
        """Forward function. """

        if train or self.stage_pct == "tokenizer":
            # Encoder of Tokenizer, Get the PCT groundtruth class labels.
            joints_coord, joints_visible, bs \
                = joints[:,:,:-1], joints[:,:,-1].bool(), joints.shape[0]
            device = joints.device
            # joints.shape = (bs, 17, 3), bs = 16, joints_coord.shape = (bs, 17, 2), joints_visible.shape = (bs, 17)
            # 第三维度为3，前两个数值为关节点平面坐标x y，最后一个数值为布尔0或1，代表每个关节点是否可见
            # 位置编码
            # --- 步骤 1: 使用 InvisiblePointEstimator 估计不可见点 ---
            # 无论训练还是推理，都先尝试估计，以观察其效果
            # 注意：如果训练时GT中的不可见点已经是(0,0)，估计器会尝试填充
            #       如果推理时输入有关节点是(0,0)且被标记为不可见，也会尝试填充
            coords_for_embedding = self.invisible_estimator.estimate_invisible(
                joints_coord.clone(), # 使用副本
                joints_visible.clone()
            )

            encode_feat = self.start_embed(coords_for_embedding) + self.position_encoding(joints_coord)
            # encode_feat.shape = (bs, 17, 256),应该是进行了embedding操作，将关节点的平面坐标映射到了256维的空间中
            # 每个关节点的平面坐标x y经过embedding后，都被映射到了256维的空间中，形成了一个256维的向量
            if self.guide_ratio > 0:
                joints_feature = joints_feature.view(bs, 17, 32, 32)
                joints_feature = self.se(joints_feature)
                joints_feature = joints_feature.view(bs, 17, -1)
                encode_img_feat = self.start_img_embed(joints_feature)
                encode_feat = torch.cat((encode_feat, encode_img_feat), dim=2)
            # joints_feature.shape = (bs, 17, 1024), 应该是关节特征的提取，encode_img_feat.shape = (bs, 17, 256)，这个是对特征提取之后做嵌入操作，将特征映射到了256维的空间中
            # encode_feat.shape = (bs, 17, 512)，将两个特征拼接起来，形成了一个512维的向量，这个向量就是每个关节点的特征向量
            # encode_feat是对关节点坐标的嵌入操作，encode_img_feat是对图像特征的嵌入操作，二者拼接到了一起，self.guide_ratio控制关节点特征和图像特征的比例

            # if train and self.stage_pct == "tokenizer":
            #     rand_mask_ind = torch.rand(
            #         joints_visible.shape, device=joints.device) > self.drop_rate
            #     joints_visible = torch.logical_and(rand_mask_ind, joints_visible) 
            # # 掩码操作，随机生成一个掩码，掩码的值大于self.drop_rate，然后将掩码和joints_visible进行逻辑与操作，得到新的joints_visible
            # # joints_visible.shape = (bs, 17)，joints_visible是一个布尔值，代表每个关节点是否可见，掩码操作是为了随机丢弃一些关节点，防止过拟合 

            # # mask_tokens = self.invisible_token.expand(bs, joints.shape[1], -1) # mask_tokens.shape = (bs, 17, 512)
            # # 创建一个形状为 (bs, 17, 512) 的 mask_tokens 张量，用于替换不可见关节点的特征向量
            # w = joints_visible.unsqueeze(-1).type_as(encode_feat) #原为mask_tokens
            # # 创建一个形状为 (bs, 17, 1) 的 w 张量，表示哪些关节点是可见的
            # # 为所有特征位置生成高斯噪声
            # noise = torch.randn_like(encode_feat) * self.mask_noise_scale
            # encode_feat = encode_feat * w + noise * (1 - w)# encode_feat.shape = (bs, 17, 512) mask_tokens.shape = (bs, 17, 512) w.shape = (bs, 17, 1)
            # # encode_feat是关节点的特征向量，mask_tokens是一个几乎全0的向量，encode_feat * w + mask_tokens * (1 - w)是一个掩码操作，将不可见的关节点的特征向量设置为全0向量
            current_joints_visible = joints_visible
    
                    
            for num_layer in self.encoder:
                encode_feat = num_layer(encode_feat) # encode_feat.shape = (bs, 17, 512)
            encode_feat = self.encoder_layer_norm(encode_feat) # encode_feat.shape = (bs, 17, 512)
            
            # 分组处理特征
            head_token_feat, head_encodings, head_indices, head_feat = self.process_group_features(
                encode_feat, self.head_indices, 'head', bs, device)
                
            torso_token_feat, torso_encodings, torso_indices, torso_feat = self.process_group_features(
                encode_feat, self.torso_indices, 'torso', bs, device)
                
            mid_limb_token_feat, mid_limb_encodings, mid_limb_indices, mid_limb_feat = self.process_group_features(
                encode_feat, self.mid_limb_indices, 'mid_limb', bs, device)
                
            end_limb_token_feat, end_limb_encodings, end_limb_indices, end_limb_feat = self.process_group_features(
                encode_feat, self.end_limb_indices, 'end_limb', bs, device)
            # 合并索引用于返回
            encoding_indices = torch.cat([head_indices, torso_indices, mid_limb_indices, end_limb_indices], dim=0)

            # 在tokenizer模式下更新各组码本
            if train and self.stage_pct == "tokenizer":
                # 保存各组的编码和特征以供码本更新使用
                self.head_encodings = head_encodings
                self.head_feat = head_feat
                self.torso_encodings = torso_encodings
                self.torso_feat = torso_feat
                self.mid_limb_encodings = mid_limb_encodings
                self.mid_limb_feat = mid_limb_feat
                self.end_limb_encodings = end_limb_encodings
                self.end_limb_feat = end_limb_feat
                # 更新各组码本
                self.update_codebook(head_encodings, head_feat, 'head', train)
                self.update_codebook(torso_encodings, torso_feat, 'torso', train)
                self.update_codebook(mid_limb_encodings, mid_limb_feat, 'mid_limb', train)
                self.update_codebook(end_limb_encodings, end_limb_feat, 'end_limb', train)
                
                # 计算量化损失
                e_latent_loss = (
                    F.mse_loss(head_token_feat.detach(), head_feat) +
                    F.mse_loss(torso_token_feat.detach(), torso_feat) +
                    F.mse_loss(mid_limb_token_feat.detach(), mid_limb_feat) +
                    F.mse_loss(end_limb_token_feat.detach(), end_limb_feat)
                ) / 4
                
                # 应用直通估计器
                head_token_feat = head_feat + (head_token_feat - head_feat).detach()
                torso_token_feat = torso_feat + (torso_token_feat - torso_feat).detach()
                mid_limb_token_feat = mid_limb_feat + (mid_limb_token_feat - mid_limb_feat).detach()
                end_limb_token_feat = end_limb_feat + (end_limb_token_feat - end_limb_feat).detach()
            else:
                e_latent_loss = None
                
            # 对每组特征进行重塑
            head_token_feat = head_token_feat.view(bs, -1, self.token_dim)
            torso_token_feat = torso_token_feat.view(bs, -1, self.token_dim)
            mid_limb_token_feat = mid_limb_token_feat.view(bs, -1, self.token_dim)
            end_limb_token_feat = end_limb_token_feat.view(bs, -1, self.token_dim)
            
            # 合并所有组的特征
            combined_token_feat = torch.cat([
                head_token_feat, torso_token_feat, mid_limb_token_feat, end_limb_token_feat
            ], dim=1)

            # 使用线性映射将维度从 token_num*4 映射回 token_num
            combined_token_feat = combined_token_feat.transpose(2, 1)  # [bs, token_dim, token_num*4] 16 512 136
            combined_token_feat_p = self.combined_token_projection(combined_token_feat)  # [bs, token_dim, token_num] 16 512 34
            feat_before_decoder = combined_token_feat_p # Shape [bs, token_dim, token_num]
            # combined_token_feat = combined_token_feat_p.transpose(1, 2) # [bs, token_num, token_dim] 16 34 512

            if train: # If training (either tokenizer or classifier), generate target indices
                # Quantize the final fused token features (combined_token_feat_p) using the main codebook
                feat_for_main_quantization = combined_token_feat_p.transpose(1, 2).flatten(0, 1) # Shape [bs * token_num, token_dim]
                
                # Ensure codebook is on the correct device
                main_codebook_on_device = self.codebook.to(feat_for_main_quantization.device)
                
                distances_main = torch.sum(feat_for_main_quantization**2, dim=1, keepdim=True) \
                    + torch.sum(main_codebook_on_device**2, dim=1) \
                    - 2 * torch.matmul(feat_for_main_quantization, main_codebook_on_device.t())
                
                encoding_indices = torch.argmin(distances_main, dim=1) # Shape [bs * token_num]
            # else: # Not training (tokenizer inference). final_encoding_indices_to_return remains None.
                  # This is acceptable as target labels are not used for loss in inference.
                pass

        else:
            # 在分类器模式下使用预测的类别概率
            bs = cls_logits.shape[0] // self.token_num
            encoding_indices = None
            
            # 使用主码本进行特征提取
            part_token_feat = torch.matmul(cls_logits, self.codebook)
            combined_token_feat = part_token_feat.view(bs, -1, self.token_dim)
            feat_before_decoder = combined_token_feat.transpose(1, 2) # Shape [bs, token_dim, token_num]
            e_latent_loss = None
        
        # 解码器处理部分
        combined_token_feat = self.decoder_token_mlp(feat_before_decoder)  # [bs, token_num, num_joints] 16 512 17
        decode_feat = self.decoder_start(combined_token_feat.transpose(1, 2))  # [bs, num_joints, token_dim]
        
        for num_layer in self.decoder:
            decode_feat = num_layer(decode_feat)
        decode_feat = self.decoder_layer_norm(decode_feat)
        
        recoverd_joints = self.recover_embed(decode_feat)
        
        return recoverd_joints, encoding_indices, e_latent_loss

        #     encode_feat = encode_feat.transpose(2, 1) # encode_feat.shape = (bs, 512, 17)转置操作
        #     encode_feat = self.token_mlp(encode_feat).transpose(2, 1) # encode_feat.shape = (bs, 34, 512)转置操作
        #     encode_feat = self.feature_embed(encode_feat).flatten(0,1) # encode_feat.shape = (bs*34, 512)
            
        #     distances = torch.sum(encode_feat**2, dim=1, keepdim=True) \
        #         + torch.sum(self.codebook**2, dim=1) \
        #         - 2 * torch.matmul(encode_feat, self.codebook.t())
        #     # distances.shape = (bs*34, 512)，计算encode_feat和self.codebook之间的距离
        #     encoding_indices = torch.argmin(distances, dim=1) # encoding_indices.shape = (bs*34,)
        #     encodings = torch.zeros(
        #         encoding_indices.shape[0], self.token_class_num, device=joints.device)
        #     # encodings.shape = (bs*34, 2048)，创建一个全0的张量
        #     encodings.scatter_(1, encoding_indices.unsqueeze(1), 1)
        #     #这部分代码生成了一个全零的编码张量 encodings，其形状是 (bs*34, token_class_num)。然后通过 scatter_ 方法，将 encoding_indices 对应的位置设置为 1，生成 one-hot 编码
        # else:
        #     bs = cls_logits.shape[0] // self.token_num
        #     encoding_indices = None
        
        # if self.stage_pct == "classifier":
        #     part_token_feat = torch.matmul(cls_logits, self.codebook)
        # else:
        #     part_token_feat = torch.matmul(encodings, self.codebook)

        # if train and self.stage_pct == "tokenizer": #更新码本，在训练阶段，如果 self.stage_pct 是 "tokenizer"，则更新码本
        #     # Updating Codebook using EMA
        #     dw = torch.matmul(encodings.t(), encode_feat.detach())
        #     # sync
        #     n_encodings, n_dw = encodings.numel(), dw.numel()
        #     encodings_shape, dw_shape = encodings.shape, dw.shape
        #     combined = torch.cat((encodings.flatten(), dw.flatten()))
        #     # dist.all_reduce(combined) # math sum
        #     sync_encodings, sync_dw = torch.split(combined, [n_encodings, n_dw])
        #     sync_encodings, sync_dw = \
        #         sync_encodings.view(encodings_shape), sync_dw.view(dw_shape)

        #     self.ema_cluster_size = self.ema_cluster_size * self.decay + \
        #                             (1 - self.decay) * torch.sum(sync_encodings, 0)
            
        #     n = torch.sum(self.ema_cluster_size.data)
        #     self.ema_cluster_size = (
        #         (self.ema_cluster_size + 1e-5)
        #         / (n + self.token_class_num * 1e-5) * n)
            
        #     self.ema_w = self.ema_w * self.decay + (1 - self.decay) * sync_dw
        #     self.codebook = self.ema_w / self.ema_cluster_size.unsqueeze(1)
        #     e_latent_loss = F.mse_loss(part_token_feat.detach(), encode_feat)
        #     part_token_feat = encode_feat + (part_token_feat - encode_feat).detach()
        # else:
        #     e_latent_loss = None
        
        # # Decoder of Tokenizer, Recover the joints.解码特征以恢复关节点
        # part_token_feat = part_token_feat.view(bs, -1, self.token_dim) 16 34 512
        # part_token_feat = part_token_feat.transpose(2,1) 16 512 34
        # part_token_feat = self.decoder_token_mlp(part_token_feat).transpose(2,1) 16 17 512
        # decode_feat = self.decoder_start(part_token_feat)

        # for num_layer in self.decoder:
        #     decode_feat = num_layer(decode_feat)
        # decode_feat = self.decoder_layer_norm(decode_feat)

        # recoverd_joints = self.recover_embed(decode_feat)

        # return recoverd_joints, encoding_indices, e_latent_loss

    def get_loss(self, output_joints, joints, e_latent_loss):
        """Calculate loss for training tokenizer.

        Note:
            batch_size: N
            num_keypoints: K

        Args:
            output_joints (torch.Tensor[NxKx3]): Recovered joints.
            joints(torch.Tensor[NxKx3]): Target joints.
            e_latent_loss(torch.Tensor[1]): Loss for training codebook.
        """

        losses = dict()

        kpt_loss, e_latent_loss = self.loss(output_joints, joints, e_latent_loss)
        # 新的损失函数
        grouped_loss = self.grouped_l2_loss(output_joints, joints)

        # 加权平均
        total_loss = 0.8 * kpt_loss + 0.2 * grouped_loss

        losses['joint_loss'] = total_loss
        losses['e_latent_loss'] = e_latent_loss

        return losses

    def init_weights(self, pretrained=""):
        """Initialize model weights."""

        parameters_names = set()
        for name, _ in self.named_parameters():
            parameters_names.add(name)

        buffers_names = set()
        for name, _ in self.named_buffers():
            buffers_names.add(name)

        if os.path.isfile(pretrained):
            assert (self.stage_pct == "classifier"), \
                "Training tokenizer does not need to load model"
            pretrained_state_dict = torch.load(pretrained, 
                            map_location=lambda storage, loc: storage)

            need_init_state_dict = {}

            for name, m in pretrained_state_dict['state_dict'].items():
                if 'keypoint_head.tokenizer.' in name:
                    name = name.replace('keypoint_head.tokenizer.', '')
                if name in parameters_names or name in buffers_names:
                    need_init_state_dict[name] = m
            self.load_state_dict(need_init_state_dict, strict=True)
        else:
            if self.stage_pct == "classifier":
                print('If you are training a classifier, '\
                    'must check that the well-trained tokenizer '\
                    'is located in the correct path.')
