#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MPII训练前检查脚本
检查所有必要的文件和配置是否正确
"""

import os
import json
from mmcv import Config

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (不存在)")
        return False

def check_directory_exists(dir_path, description):
    """检查目录是否存在"""
    if os.path.exists(dir_path) and os.path.isdir(dir_path):
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} (不存在)")
        return False

def check_annotation_file(ann_file):
    """检查标注文件格式"""
    try:
        with open(ann_file, 'r') as f:
            data = json.load(f)
        
        if isinstance(data, list) and len(data) > 0:
            sample = data[0]
            if 'joints' in sample and 'joints_vis' in sample:
                joints = sample['joints']
                if len(joints) == 16:  # MPII应该有16个关键点
                    print(f"✅ 标注文件格式正确: {len(data)} 个样本, 16个关键点")
                    return True
                else:
                    print(f"❌ 关键点数量错误: {len(joints)} (应该是16)")
                    return False
            else:
                print(f"❌ 标注文件缺少必要字段: joints, joints_vis")
                return False
        else:
            print(f"❌ 标注文件格式错误")
            return False
    except Exception as e:
        print(f"❌ 读取标注文件失败: {e}")
        return False

def main():
    print("🔍 MPII训练前检查")
    print("=" * 50)
    
    all_good = True
    
    # 1. 检查配置文件
    print("\n📁 配置文件检查:")
    config_files = [
        ('configs/pct_mpii_train.py', '从头训练配置文件'),
        ('configs/pct_mpii_finetune.py', 'COCO微调配置文件'),
        ('configs/mpii.py', 'MPII数据集配置'),
    ]
    
    for file_path, desc in config_files:
        if not check_file_exists(file_path, desc):
            all_good = False
    
    # 2. 检查数据集文件
    print("\n📊 数据集文件检查:")
    data_root = 'data/MPII/mpii_human_pose_v1'
    
    # 检查目录
    if not check_directory_exists(data_root, 'MPII数据集根目录'):
        all_good = False
        print("请确保MPII数据集已正确下载和解压")
        return
    
    if not check_directory_exists(f'{data_root}/images', '图像目录'):
        all_good = False
    
    if not check_directory_exists(f'{data_root}/annotations', '标注目录'):
        all_good = False
    
    # 检查标注文件
    ann_files = [
        (f'{data_root}/annotations/train.json', '训练标注文件'),
        (f'{data_root}/annotations/valid.json', '验证标注文件'),
        (f'{data_root}/annotations/mpii_gt_val.mat', '评估GT文件'),
    ]
    
    for file_path, desc in ann_files:
        if not check_file_exists(file_path, desc):
            all_good = False
            if 'mpii_gt_val.mat' in file_path:
                print("  💡 提示: 可以从gt_valid.mat复制: copy \"data\\MPII\\mpii_human_pose_v1\\annotations\\gt_valid.mat\" \"data\\MPII\\mpii_human_pose_v1\\annotations\\mpii_gt_val.mat\"")
    
    # 检查标注文件格式
    if os.path.exists(f'{data_root}/annotations/train.json'):
        print(f"\n📋 训练标注文件格式检查:")
        if not check_annotation_file(f'{data_root}/annotations/train.json'):
            all_good = False
    
    if os.path.exists(f'{data_root}/annotations/valid.json'):
        print(f"\n📋 验证标注文件格式检查:")
        if not check_annotation_file(f'{data_root}/annotations/valid.json'):
            all_good = False
    
    # 3. 检查预训练权重
    print("\n🏋️ 预训练权重检查:")
    weight_files = [
        ('work_dirs/pct_base_classifier/best_AP_epoch_282.pth', 'COCO预训练权重'),
        ('weights/heatmap/swin_base.pth', 'Backbone预训练权重'),
    ]
    
    for file_path, desc in weight_files:
        if not check_file_exists(file_path, desc):
            all_good = False
    
    # 4. 检查训练脚本
    print("\n🚀 训练脚本检查:")
    script_files = [
        ('tools/train.py', '训练脚本'),
        ('train_mpii.py', 'MPII训练启动脚本'),
    ]
    
    for file_path, desc in script_files:
        if not check_file_exists(file_path, desc):
            all_good = False
    
    # 5. 检查配置文件内容
    print("\n⚙️ 配置文件内容检查:")
    try:
        cfg = Config.fromfile('configs/pct_mpii_train.py')
        
        # 检查关键配置
        if cfg.total_epochs == 120:
            print("✅ 训练轮数: 120 epochs")
        else:
            print(f"❌ 训练轮数: {cfg.total_epochs} (应该是120)")
            all_good = False
        
        if cfg.evaluation.interval == 3:
            print("✅ 验证间隔: 每3个epoch")
        else:
            print(f"❌ 验证间隔: 每{cfg.evaluation.interval}个epoch (应该是3)")
            all_good = False
        
        if cfg.evaluation.metric == 'PCKh':
            print("✅ 评估指标: PCKh")
        else:
            print(f"❌ 评估指标: {cfg.evaluation.metric} (应该是PCKh)")
            all_good = False
        
        if cfg.channel_cfg.num_output_channels == 16:
            print("✅ 关键点数量: 16个 (MPII)")
        else:
            print(f"❌ 关键点数量: {cfg.channel_cfg.num_output_channels} (应该是16)")
            all_good = False
            
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        all_good = False
    
    # 6. 总结
    print("\n" + "=" * 50)
    if all_good:
        print("🎉 所有检查通过！可以开始训练了")
        print("\n🚀 启动训练命令:")
        print("python train_mpii.py")
        print("\n📊 训练配置:")
        print("- 总轮数: 120 epochs")
        print("- 验证频率: 每3个epoch")
        print("- 评估指标: PCKh")
        print("- 批量大小: 16 (训练), 32 (验证)")
        print("- 学习率: 5e-4")
        print("- 工作目录: work_dirs/pct_mpii_train")
    else:
        print("❌ 检查发现问题，请先解决上述问题再开始训练")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
