#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MPII数据集训练脚本
使用PCT模型在MPII数据集上进行训练，每3个epoch验证一次
"""

import os
import sys
import argparse
import subprocess

def main():
    parser = argparse.ArgumentParser(description='Train PCT model on MPII dataset')
    parser.add_argument('--config', 
                       default='configs/pct_mpii_train.py',
                       help='train config file path')
    parser.add_argument('--work-dir', 
                       default='work_dirs/pct_mpii_train',
                       help='the dir to save logs and models')
    parser.add_argument('--resume-from', 
                       default=None,
                       help='the checkpoint file to resume from')
    parser.add_argument('--load-from',
                       default=None,
                       help='the checkpoint file to load from')
    parser.add_argument('--pretrain-type',
                       choices=['backbone', 'coco-early', 'coco-best', 'none'],
                       default='backbone',
                       help='type of pretrained weights to use')
    parser.add_argument('--gpus', 
                       type=int, 
                       default=1,
                       help='number of gpus to use')
    parser.add_argument('--seed', 
                       type=int, 
                       default=42,
                       help='random seed')
    parser.add_argument('--deterministic', 
                       action='store_true',
                       help='whether to set deterministic options for CUDNN backend')
    
    args = parser.parse_args()
    
    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        print(f"错误: 配置文件不存在: {args.config}")
        return 1
    
    # 检查数据集是否存在
    data_root = 'data/MPII/mpii_human_pose_v1'
    train_ann = f'{data_root}/annotations/train.json'
    val_ann = f'{data_root}/annotations/valid.json'
    img_dir = f'{data_root}/images'
    
    if not os.path.exists(train_ann):
        print(f"错误: 训练标注文件不存在: {train_ann}")
        return 1
        
    if not os.path.exists(val_ann):
        print(f"错误: 验证标注文件不存在: {val_ann}")
        return 1
        
    if not os.path.exists(img_dir):
        print(f"错误: 图像目录不存在: {img_dir}")
        return 1
    
    # 创建工作目录
    os.makedirs(args.work_dir, exist_ok=True)

    # 根据预训练类型设置权重文件
    pretrain_weights = {
        'backbone': 'weights/heatmap/swin_base.pth',
        'coco-early': 'work_dirs/pct_base_classifier/epoch_50.pth',  # 假设有早期权重
        'coco-best': 'work_dirs/pct_base_classifier/best_AP_epoch_282.pth',
        'none': None
    }

    selected_weight = pretrain_weights.get(args.pretrain_type)
    if selected_weight and not os.path.exists(selected_weight):
        print(f"警告: 预训练权重文件不存在: {selected_weight}")
        if args.pretrain_type == 'coco-early':
            print("回退到backbone预训练权重")
            selected_weight = pretrain_weights['backbone']
        elif args.pretrain_type == 'coco-best':
            print("回退到backbone预训练权重")
            selected_weight = pretrain_weights['backbone']
    
    # 构建训练命令
    cmd = [
        'python', 'tools/train.py',
        args.config,
        '--work-dir', args.work_dir,
        '--seed', str(args.seed)
    ]
    
    if args.resume_from:
        cmd.extend(['--resume-from', args.resume_from])
    
    if args.load_from:
        cmd.extend(['--load-from', args.load_from])
    elif selected_weight:
        cmd.extend(['--cfg-options', f'model.pretrained={selected_weight}'])
        
    if args.deterministic:
        cmd.append('--deterministic')
    
    print("开始MPII数据集训练...")
    print(f"配置文件: {args.config}")
    print(f"工作目录: {args.work_dir}")
    print(f"训练轮数: 120 epochs")
    print(f"验证频率: 每3个epoch验证一次")
    print(f"使用GPU数量: {args.gpus}")
    print(f"随机种子: {args.seed}")
    print(f"预训练类型: {args.pretrain_type}")

    if args.resume_from:
        print(f"从checkpoint恢复: {args.resume_from}")
    elif args.load_from:
        print(f"加载预训练权重: {args.load_from}")
    elif selected_weight:
        print(f"使用预训练权重: {selected_weight}")
    else:
        print("从头开始训练 (无预训练权重)")
    
    print()
    print("执行命令:")
    print(' '.join(cmd))
    print()
    print("训练配置说明:")
    print("- 总训练轮数: 120 epochs")
    print("- 验证间隔: 每3个epoch")
    print("- 保存间隔: 每3个epoch")
    print("- 评估指标: PCKh (MPII标准)")
    print("- 学习率: 5e-4 (微调)")
    print("- 批量大小: 16 (训练), 32 (验证)")
    print("- 数据增强: 颜色抖动, 网格丢弃, 随机翻转等")
    print()
    
    # 执行训练
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n训练完成!")
        print(f"模型和日志已保存到: {args.work_dir}")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"\n训练失败，错误代码: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("\n训练被用户中断")
        return 1
    except Exception as e:
        print(f"\n发生错误: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
