#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCHuman数据集训练脚本
基于原始PCT-base-classifier配置进行训练和测试
"""

import os
import sys
import argparse
import subprocess

def check_prerequisites():
    """检查训练前提条件"""
    print("🔍 检查训练前提条件...")
    
    # 检查配置文件
    config_file = 'configs/pct_ochuman.py'
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    print(f"✅ 配置文件存在: {config_file}")
    
    # 检查数据集
    data_root = 'data/OCHuman'
    required_files = [
        'ochuman_coco_format_test_range_0.00_1.00.json',
        'ochuman_coco_format_val_range_0.00_1.00.json',
        'images'
    ]
    
    for file in required_files:
        path = os.path.join(data_root, file)
        if not os.path.exists(path):
            print(f"❌ 数据文件不存在: {path}")
            return False
        print(f"✅ 数据文件存在: {path}")
    
    # 检查预训练权重
    weights = [
        'weights/heatmap/swin_base.pth',
        'weights/tokenizer/test_codebook/best_AP_epoch_11.pth'
    ]
    
    for weight in weights:
        if not os.path.exists(weight):
            print(f"❌ 预训练权重不存在: {weight}")
            return False
        print(f"✅ 预训练权重存在: {weight}")
    
    # 检查训练脚本
    if not os.path.exists('tools/train.py'):
        print("❌ 训练脚本不存在: tools/train.py")
        return False
    print("✅ 训练脚本存在: tools/train.py")
    
    return True

def run_training(config, work_dir, resume_from=None, seed=42):
    """运行训练"""
    cmd = [
        'python', 'tools/train.py',
        config,
        '--work-dir', work_dir,
        '--seed', str(seed)
    ]
    
    if resume_from:
        cmd.extend(['--resume-from', resume_from])
    
    print(f"\n{'='*60}")
    print("🚀 开始OCHuman数据集训练")
    print(f"{'='*60}")
    print(f"配置文件: {config}")
    print(f"工作目录: {work_dir}")
    print(f"随机种子: {seed}")
    if resume_from:
        print(f"恢复训练: {resume_from}")
    print(f"执行命令: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ 训练完成!")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 训练失败，错误代码: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print(f"\n⚠️ 训练被用户中断")
        return 1

def run_testing(config, checkpoint, work_dir):
    """运行测试"""
    cmd = [
        'python', 'tools/test.py',
        config,
        checkpoint,
        '--eval', 'mAP',
        '--work-dir', work_dir
    ]
    
    print(f"\n{'='*60}")
    print("🧪 开始OCHuman数据集测试")
    print(f"{'='*60}")
    print(f"配置文件: {config}")
    print(f"模型权重: {checkpoint}")
    print(f"工作目录: {work_dir}")
    print(f"执行命令: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ 测试完成!")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 测试失败，错误代码: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print(f"\n⚠️ 测试被用户中断")
        return 1

def find_best_checkpoint(work_dir):
    """找到最佳的checkpoint文件"""
    import glob
    
    # 首先查找best文件
    pattern = os.path.join(work_dir, 'best_AP_epoch_*.pth')
    checkpoints = glob.glob(pattern)
    if checkpoints:
        return max(checkpoints, key=os.path.getmtime)
    
    # 如果没有best文件，找最新的epoch文件
    pattern = os.path.join(work_dir, 'epoch_*.pth')
    checkpoints = glob.glob(pattern)
    if checkpoints:
        return max(checkpoints, key=os.path.getmtime)
    
    return None

def main():
    parser = argparse.ArgumentParser(description='OCHuman dataset training and testing')
    parser.add_argument('--mode', 
                       choices=['train', 'test', 'both'],
                       default='both',
                       help='运行模式: train, test, or both')
    parser.add_argument('--config', 
                       default='configs/pct_ochuman.py',
                       help='配置文件路径')
    parser.add_argument('--work-dir', 
                       default='work_dirs/pct_ochuman',
                       help='工作目录')
    parser.add_argument('--resume-from', 
                       default=None,
                       help='从checkpoint恢复训练')
    parser.add_argument('--checkpoint', 
                       default=None,
                       help='测试用的checkpoint文件')
    parser.add_argument('--seed', 
                       type=int, 
                       default=42,
                       help='随机种子')
    
    args = parser.parse_args()
    
    print("🎯 PCT模型在OCHuman数据集上的训练和测试")
    print("=" * 60)
    
    # 检查前提条件
    if not check_prerequisites():
        print("\n❌ 前提条件检查失败，请确保所有必要文件存在")
        return 1
    
    print("\n✅ 所有前提条件检查通过!")
    
    # 创建工作目录
    os.makedirs(args.work_dir, exist_ok=True)
    
    # 训练阶段
    if args.mode in ['train', 'both']:
        print("\n📋 训练配置说明:")
        print("- 模型: PCT (Pose Classification Transformer)")
        print("- 数据集: OCHuman (17个关键点)")
        print("- 预训练: SwinV2 backbone + Tokenizer")
        print("- 训练轮数: 120 epochs")
        print("- 验证频率: 每3个epoch")
        print("- 评估指标: mAP")
        print("- 批量大小: 32")
        print("- 学习率: 8e-4")
        
        result = run_training(
            config=args.config,
            work_dir=args.work_dir,
            resume_from=args.resume_from,
            seed=args.seed
        )
        
        if result != 0:
            print("❌ 训练失败，停止执行")
            return result
    
    # 测试阶段
    if args.mode in ['test', 'both']:
        # 确定要使用的checkpoint
        if args.checkpoint:
            checkpoint = args.checkpoint
        else:
            checkpoint = find_best_checkpoint(args.work_dir)
        
        if not checkpoint:
            print("❌ 未找到可用的checkpoint文件")
            if args.mode == 'test':
                print("请使用 --checkpoint 参数指定checkpoint文件")
                return 1
            else:
                print("跳过测试阶段")
                return 0
        
        print(f"\n📋 测试配置说明:")
        print("- 使用最佳训练模型进行测试")
        print("- 评估指标: mAP, AP@0.5, AP@0.75")
        print("- 测试集: OCHuman验证集")
        print(f"- 模型文件: {checkpoint}")
        
        result = run_testing(
            config=args.config,
            checkpoint=checkpoint,
            work_dir=args.work_dir
        )
        
        if result != 0:
            print("❌ 测试失败")
            return result
    
    print(f"\n{'='*60}")
    print("🎉 所有任务完成!")
    print(f"{'='*60}")
    
    if args.mode in ['train', 'both']:
        print(f"训练结果保存在: {args.work_dir}")
        best_checkpoint = find_best_checkpoint(args.work_dir)
        if best_checkpoint:
            print(f"最佳模型: {best_checkpoint}")
    
    print("\n📊 OCHuman数据集特点:")
    print("- 包含复杂遮挡场景的人体姿态")
    print("- 17个COCO格式关键点")
    print("- 适合测试模型在困难场景下的性能")
    
    print("\n🔧 后续可以尝试:")
    print("- 调整学习率和训练轮数")
    print("- 修改数据增强策略")
    print("- 尝试不同的预训练权重")
    print("- 在其他数据集上进行迁移学习")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
