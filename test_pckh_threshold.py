#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PCKh阈值设置是否生效
"""

import torch
import numpy as np
from mmcv import Config
import models
from mmpose.models import build_posenet
from mmpose.datasets import build_dataset

def test_pckh_threshold():
    print("🔍 测试PCKh阈值设置...")
    
    # 测试两种配置
    configs = [
        {
            'name': 'PCKh@0.1 (默认)',
            'metric_options': {}  # 空配置，使用默认
        },
        {
            'name': 'PCKh@0.5 (修改)',
            'metric_options': {'thr': 0.5}
        }
    ]
    
    for config_test in configs:
        print(f"\n📊 测试配置: {config_test['name']}")
        
        # 加载配置
        cfg = Config.fromfile('configs/pct_mpii_test_stage1.py')
        
        # 修改evaluation配置
        cfg.evaluation['metric_options'] = config_test['metric_options']
        
        # 构建数据集
        dataset = build_dataset(cfg.data.test)
        
        # 模拟一些预测结果
        fake_results = []
        for i in range(10):  # 只测试10个样本
            # 创建假的预测结果
            pred_joints = np.random.rand(16, 3) * 200 + 28  # 随机坐标
            fake_results.append({
                'preds': pred_joints,
                'boxes': np.array([[0, 0, 256, 256]]),
                'image_paths': [f'test_{i}.jpg'],
                'bbox_ids': [i]
            })
        
        # 评估
        try:
            eval_results = dataset.evaluate(fake_results, metric='PCKh', **config_test['metric_options'])
            print(f"✅ 评估成功")
            print(f"📈 结果: {eval_results}")
        except Exception as e:
            print(f"❌ 评估失败: {e}")

def check_training_config_history():
    """检查训练时的配置"""
    print("\n🔍 检查当前第一阶段配置...")
    
    cfg = Config.fromfile('configs/pct_mpii_tokenizer_stage1.py')
    
    print("📋 当前evaluation配置:")
    print(f"  interval: {cfg.evaluation.get('interval', 'N/A')}")
    print(f"  metric: {cfg.evaluation.get('metric', 'N/A')}")
    print(f"  save_best: {cfg.evaluation.get('save_best', 'N/A')}")
    print(f"  metric_options: {cfg.evaluation.get('metric_options', 'N/A')}")
    
    # 检查是否有metric_options
    if 'metric_options' not in cfg.evaluation:
        print("⚠️ 没有metric_options配置，可能使用默认PCKh@0.1")
        return False
    elif cfg.evaluation.metric_options.get('thr') == 0.5:
        print("✅ 配置了PCKh@0.5")
        return True
    else:
        print(f"⚠️ 配置了其他阈值: {cfg.evaluation.metric_options.get('thr')}")
        return False

if __name__ == '__main__':
    # 检查当前配置
    is_correct = check_training_config_history()
    
    if not is_correct:
        print("\n💡 建议:")
        print("1. 第一阶段训练时可能使用了PCKh@0.1")
        print("2. 这可能导致模型优化目标不合理")
        print("3. 建议重新训练第一阶段，使用PCKh@0.5")
    
    # 测试阈值设置
    # test_pckh_threshold()  # 注释掉，因为需要完整的数据集
