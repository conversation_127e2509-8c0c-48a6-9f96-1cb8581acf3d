# PCT MPII数据集训练指南

## 📖 概述

本指南详细介绍如何在MPII数据集上进行PCT（Pose Compositional Tokens）模型的两阶段训练。MPII是一个2D人体姿态估计数据集，包含16个关键点，与COCO数据集的17个关键点略有不同。

## 🎯 训练策略

### 两阶段训练方法
1. **第一阶段 (Tokenizer训练)**: 学习关键点的token表示
2. **第二阶段 (Classifier训练)**: 学习从图像到token的映射

### 数据集差异
- **COCO**: 17个关键点（包含面部关键点）
- **MPII**: 16个关键点（主要是身体关键点）

## 🔧 技术实现

### 自适应架构
为了保持与原始COCO训练的兼容性，我们实现了自适应的tokenizer和head：

- `models/pct_tokenizer_adaptive.py`: 支持不同关键点数量的tokenizer
- `models/pct_head_adaptive.py`: 自适应的PCT head
- 原始文件 `pct_tokenizer.py` 和 `pct_head.py` 保持不变

### 关键点映射
MPII的16个关键点顺序：
```
0: right_ankle    1: right_knee     2: right_hip      3: left_hip
4: left_knee      5: left_ankle     6: pelvis         7: thorax
8: upper_neck     9: head_top       10: right_wrist   11: right_elbow
12: right_shoulder 13: left_shoulder 14: left_elbow    15: left_wrist
```

### 分组损失策略
针对MPII的关键点分组：
- **头部**: [6, 7, 8, 9] (pelvis, thorax, upper_neck, head_top)
- **手臂**: [10, 11, 12, 13, 14, 15] (wrists, elbows, shoulders)
- **腿部**: [0, 1, 2, 3, 4, 5] (ankles, knees, hips)

## 📁 文件结构

### 配置文件
```
configs/
├── pct_mpii_tokenizer_stage1.py    # 第一阶段配置
├── pct_mpii_classifier_stage2.py   # 第二阶段配置
├── mpii.py                          # MPII数据集基础配置
└── pct_mpii_train.py               # 原始MPII配置（可删除）
```

### 模型文件
```
models/
├── pct_tokenizer_adaptive.py       # 自适应tokenizer（新增）
├── pct_head_adaptive.py            # 自适应head（新增）
├── pct_tokenizer.py                # 原始tokenizer（保留）
└── pct_head.py                     # 原始head（保留）
```

### 训练脚本
```
├── train_mpii_two_stage.py         # 两阶段训练脚本（新增）
├── test_model_init.py              # 模型测试脚本（可删除）
└── convert_coco_to_mpii_weights.py # 权重转换脚本（保留）
```

## 🚀 使用方法

### 1. 完整两阶段训练
```bash
python train_mpii_two_stage.py
```

### 2. 单独运行各阶段
```bash
# 第一阶段：Tokenizer训练
python tools/train.py configs/pct_mpii_tokenizer_stage1.py --work-dir work_dirs/stage1

# 第二阶段：Classifier训练（需要先设置tokenizer权重路径）
python tools/train.py configs/pct_mpii_classifier_stage2.py --work-dir work_dirs/stage2
```

### 3. 测试模型
```bash
python tools/test.py configs/pct_mpii_classifier_stage2.py work_dirs/stage2/best_PCKh_epoch_X.pth --eval PCKh
```

## ⚙️ 训练参数

### 第一阶段 (Tokenizer)
- **训练轮数**: 120 epochs
- **学习率**: 1e-4
- **批量大小**: 16
- **优化器**: AdamW
- **评估指标**: PCKh@0.5
- **验证频率**: 每3个epoch

### 第二阶段 (Classifier)
- **训练轮数**: 120 epochs
- **学习率**: 8e-4
- **Tokenizer学习率**: 0.1x (冻结大部分参数)
- **批量大小**: 16
- **优化器**: AdamW
- **评估指标**: PCKh@0.5
- **验证频率**: 每3个epoch

## 📊 预期结果

### 训练过程
- **第一阶段**: 主要优化VQ-VAE损失和关键点重建损失
- **第二阶段**: 主要优化分类损失和最终的关键点检测精度

### 性能指标
- **评估指标**: PCKh (Percentage of Correct Keypoints with head normalization)
- **预期精度**: 根据MPII数据集的复杂度，预期达到合理的PCKh分数

## 🔍 故障排除

### 常见问题
1. **维度不匹配**: 确保使用自适应模型 (`PCT_Head_Adaptive`)
2. **内存不足**: 减少批量大小或使用梯度累积
3. **收敛慢**: 调整学习率或增加warmup步数

### 调试工具
```bash
# 测试模型初始化
python test_model_init.py

# 检查配置文件
python -c "from mmcv import Config; cfg = Config.fromfile('configs/pct_mpii_tokenizer_stage1.py'); print('配置正确')"
```

## 🔄 与COCO训练的兼容性

### 保持兼容性
- 原始COCO训练脚本和配置完全不受影响
- 可以同时进行COCO和MPII训练
- 通过配置文件中的 `use_adaptive_tokenizer` 参数控制

### 切换数据集
```python
# COCO训练（原始方式）
model = dict(
    keypoint_head=dict(
        type='PCT_Head',  # 使用原始head
        num_joints=17,
        # ...
    )
)

# MPII训练（新方式）
model = dict(
    keypoint_head=dict(
        type='PCT_Head_Adaptive',  # 使用自适应head
        num_joints=16,
        dataset_type='MPII',
        use_adaptive_tokenizer=True,
        # ...
    )
)
```

## 📈 监控训练

### 关键指标
- **joint_loss**: 关键点重建损失
- **e_latent_loss**: VQ-VAE量化损失
- **PCKh**: 验证集上的关键点检测精度

### 日志位置
```
work_dirs/
├── pct_mpii_tokenizer_stage1/     # 第一阶段日志和权重
└── pct_mpii_classifier_stage2/    # 第二阶段日志和权重
```

## 🎉 完成后的使用

### 权重文件
- **第一阶段**: `work_dirs/stage1/best_PCKh_epoch_X.pth`
- **第二阶段**: `work_dirs/stage2/best_PCKh_epoch_X.pth`

### 推理使用
第二阶段训练完成的模型可以直接用于MPII格式的2D姿态估计任务。

---

## 📝 更新日志

- **2025-07-28**: 创建MPII两阶段训练方案
- **2025-07-28**: 实现自适应tokenizer和head
- **2025-07-28**: 解决关键点数量适配问题
- **2025-07-28**: 成功启动第一阶段训练
