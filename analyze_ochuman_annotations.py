#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析OCHuman数据集的标注格式，特别是遮挡信息
"""

import json
import numpy as np
from collections import defaultdict

def analyze_ochuman_annotations():
    """分析OCHuman标注格式"""
    
    # 加载数据
    with open('data/OCHuman/ochuman_coco_format_val_range_0.00_1.00.json', 'r') as f:
        data = json.load(f)
    
    print("🔍 OCHuman数据集标注分析")
    print("=" * 50)
    
    # 基本信息
    print(f"图像数量: {len(data['images'])}")
    print(f"标注数量: {len(data['annotations'])}")
    print(f"类别数量: {len(data['categories'])}")
    
    # 分析关键点可见性
    print("\n📊 关键点可见性分析")
    vis_stats = defaultdict(int)
    total_keypoints = 0
    occluded_count = 0
    visible_count = 0
    not_labeled_count = 0
    
    for ann in data['annotations']:
        keypoints = ann['keypoints']
        # COCO格式: [x1, y1, v1, x2, y2, v2, ...]
        # v: 0=未标注, 1=遮挡但可见, 2=可见
        for i in range(17):
            visibility = keypoints[i * 3 + 2]
            vis_stats[visibility] += 1
            total_keypoints += 1
            
            if visibility == 0:
                not_labeled_count += 1
            elif visibility == 1:
                occluded_count += 1
            elif visibility == 2:
                visible_count += 1
    
    print(f"总关键点数: {total_keypoints}")
    print(f"可见性统计:")
    for vis, count in sorted(vis_stats.items()):
        percentage = count / total_keypoints * 100
        if vis == 0:
            print(f"  - 未标注 (0): {count} ({percentage:.1f}%)")
        elif vis == 1:
            print(f"  - 遮挡但可见 (1): {count} ({percentage:.1f}%)")
        elif vis == 2:
            print(f"  - 完全可见 (2): {count} ({percentage:.1f}%)")
        else:
            print(f"  - 其他 ({vis}): {count} ({percentage:.1f}%)")
    
    # 分析每个关键点的遮挡情况
    print("\n📋 各关键点遮挡统计")
    keypoint_names = [
        'nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear',
        'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
        'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
        'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
    ]
    
    keypoint_stats = defaultdict(lambda: defaultdict(int))
    
    for ann in data['annotations']:
        keypoints = ann['keypoints']
        for i in range(17):
            visibility = keypoints[i * 3 + 2]
            keypoint_stats[i][visibility] += 1
    
    print(f"{'关键点':<15} {'未标注':<8} {'遮挡':<8} {'可见':<8} {'遮挡率':<8}")
    print("-" * 55)
    
    for i in range(17):
        name = keypoint_names[i]
        not_labeled = keypoint_stats[i][0]
        occluded = keypoint_stats[i][1]
        visible = keypoint_stats[i][2]
        total = not_labeled + occluded + visible
        
        if total > 0:
            occlusion_rate = (occluded + not_labeled) / total * 100
        else:
            occlusion_rate = 0
            
        print(f"{name:<15} {not_labeled:<8} {occluded:<8} {visible:<8} {occlusion_rate:<8.1f}%")
    
    # 分析样本的遮挡程度
    print("\n📈 样本遮挡程度分析")
    occlusion_levels = defaultdict(int)
    
    for ann in data['annotations']:
        keypoints = ann['keypoints']
        total_kpts = 17
        occluded_kpts = 0
        
        for i in range(17):
            visibility = keypoints[i * 3 + 2]
            if visibility == 0 or visibility == 1:  # 未标注或遮挡
                occluded_kpts += 1
        
        occlusion_ratio = occluded_kpts / total_kpts
        
        if occlusion_ratio == 0:
            level = "无遮挡"
        elif occlusion_ratio <= 0.3:
            level = "轻度遮挡"
        elif occlusion_ratio <= 0.6:
            level = "中度遮挡"
        else:
            level = "重度遮挡"
            
        occlusion_levels[level] += 1
    
    total_samples = len(data['annotations'])
    for level, count in occlusion_levels.items():
        percentage = count / total_samples * 100
        print(f"{level}: {count} ({percentage:.1f}%)")
    
    print("\n💡 关键发现:")
    print("1. OCHuman使用标准COCO可见性标注:")
    print("   - 0: 未标注/不可见")
    print("   - 1: 遮挡但可见")
    print("   - 2: 完全可见")
    print("2. APoc指标应该只计算遮挡关键点(visibility=1)的精度")
    print("3. 需要实现专门的OCHuman评估协议")
    
    return vis_stats, keypoint_stats, occlusion_levels

if __name__ == '__main__':
    analyze_ochuman_annotations()
