#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理MPII相关的旧文件
运行前请确认您不再需要这些文件
"""

import os
import shutil

def cleanup_mpii_files():
    """清理MPII相关的旧文件和目录"""
    
    # 要删除的文件列表
    files_to_delete = [
        # 旧文档
        'MPII_测试说明.md',
        'MPII_训练策略对比.md',
        'MPII_训练说明.md',
        
        # 测试脚本
        'test_model_init.py',
        'check_mpii_training.py', 
        'test_mpii.py',
        'train_mpii.py',
        
        # 旧配置文件
        'configs/pct_mpii_train.py',
        'configs/pct_mpii_finetune.py',
        'configs/test_coco_on_mpii.py',
    ]
    
    # 要删除的目录列表
    dirs_to_delete = [
        'work_dirs/pct_mpii_train',
        'work_dirs/pct_mpii_test', 
        'work_dirs/pct_mpii_with_coco_tokenizer',
    ]
    
    print("🗑️ 开始清理MPII相关的旧文件...")
    
    # 删除文件
    for file_path in files_to_delete:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"✅ 已删除文件: {file_path}")
            except Exception as e:
                print(f"❌ 删除文件失败 {file_path}: {e}")
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    # 删除目录
    for dir_path in dirs_to_delete:
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                print(f"✅ 已删除目录: {dir_path}")
            except Exception as e:
                print(f"❌ 删除目录失败 {dir_path}: {e}")
        else:
            print(f"⚠️ 目录不存在: {dir_path}")
    
    print("\n📋 保留的重要文件:")
    important_files = [
        'configs/pct_mpii_tokenizer_stage1.py',
        'configs/pct_mpii_classifier_stage2.py', 
        'configs/mpii.py',
        'models/pct_tokenizer_adaptive.py',
        'models/pct_head_adaptive.py',
        'train_mpii_two_stage.py',
        'convert_coco_to_mpii_weights.py',
        'MPII_Training_Guide.md'
    ]
    
    for file_path in important_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (缺失!)")
    
    print("\n🎉 清理完成!")
    print("💡 如果需要恢复文件，请使用git恢复或从备份中恢复")

if __name__ == '__main__':
    # 安全确认
    response = input("⚠️ 确认要删除这些MPII相关的旧文件吗? (输入 'yes' 确认): ")
    if response.lower() == 'yes':
        cleanup_mpii_files()
    else:
        print("❌ 取消清理操作")
