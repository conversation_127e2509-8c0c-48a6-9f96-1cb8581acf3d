#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MPII数据集测试脚本
使用PCT模型在MPII数据集上进行精度测试
"""

import os
import sys
import argparse
import subprocess

def main():
    parser = argparse.ArgumentParser(description='Test PCT model on MPII dataset')
    parser.add_argument('--config', 
                       default='configs/pct_mpii_test.py',
                       help='test config file path')
    parser.add_argument('--checkpoint', 
                       default='work_dirs/pct_base_classifier/best_AP_epoch_282.pth',
                       help='checkpoint file')
    parser.add_argument('--out', 
                       default='work_dirs/pct_mpii_test/result_keypoints.json',
                       help='output result file')
    parser.add_argument('--eval', 
                       default='PCKh',
                       help='evaluation metric')
    parser.add_argument('--work-dir',
                       default='work_dirs/pct_mpii_test',
                       help='the dir to save evaluation results')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.config):
        print(f"错误: 配置文件不存在: {args.config}")
        return 1
        
    if not os.path.exists(args.checkpoint):
        print(f"错误: 模型权重文件不存在: {args.checkpoint}")
        print("可用的权重文件:")
        weight_dir = os.path.dirname(args.checkpoint)
        if os.path.exists(weight_dir):
            for f in os.listdir(weight_dir):
                if f.endswith('.pth'):
                    print(f"  - {os.path.join(weight_dir, f)}")
        return 1
    
    # 检查数据集是否存在
    data_root = 'data/MPII/mpii_human_pose_v1'
    ann_file = f'{data_root}/annotations/valid.json'
    img_dir = f'{data_root}/images'
    
    if not os.path.exists(ann_file):
        print(f"错误: 标注文件不存在: {ann_file}")
        return 1
        
    if not os.path.exists(img_dir):
        print(f"错误: 图像目录不存在: {img_dir}")
        return 1
    
    # 创建输出目录
    os.makedirs(os.path.dirname(args.out), exist_ok=True)
    
    # 构建测试命令
    cmd = [
        'python', 'tools/test.py',
        args.config,
        args.checkpoint,
        '--out', args.out,
        '--eval', args.eval,
        '--work-dir', args.work_dir
    ]
    
    print("开始MPII数据集测试...")
    print(f"配置文件: {args.config}")
    print(f"模型权重: {args.checkpoint}")
    print(f"输出文件: {args.out}")
    print(f"评估指标: {args.eval}")
    print(f"工作目录: {args.work_dir}")
    print()
    print("执行命令:")
    print(' '.join(cmd))
    print()
    
    # 执行测试
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n测试完成!")
        print(f"结果已保存到: {args.out}")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"\n测试失败，错误代码: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n发生错误: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
