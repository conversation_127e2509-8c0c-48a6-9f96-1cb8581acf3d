#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第二阶段配置是否正确
"""

import torch
from mmcv import Config
import models  # 确保导入我们的自定义模型
from mmpose.models import build_posenet

def test_stage2_config():
    print("🔍 测试第二阶段配置...")
    
    try:
        # 加载第二阶段配置
        cfg = Config.fromfile('configs/pct_mpii_classifier_stage2.py')
        print("✅ 第二阶段配置文件加载成功")
        
        # 构建模型
        model = build_posenet(cfg.model)
        print("✅ 第二阶段模型构建成功")
        
        # 测试前向传播
        batch_size = 2
        img = torch.randn(batch_size, 3, 256, 256)
        joints = torch.randn(batch_size, 16, 3)  # MPII 16个关键点
        joints_visible = torch.ones(batch_size, 16, 3)  # 所有关键点都可见
        img_metas = [{
            'image_file': 'test.jpg', 
            'bbox_id': i,
            'center': [128, 128],  # 图像中心
            'scale': [1.0, 1.0],   # 缩放比例
            'rotation': 0,         # 旋转角度
            'bbox_score': 1.0      # bbox置信度
        } for i in range(batch_size)]
        
        print("🔍 测试第二阶段前向传播...")
        model.eval()
        with torch.no_grad():
            output = model(img, joints_3d=joints, joints_3d_visible=joints_visible, img_metas=img_metas, return_loss=False)
        print("✅ 第二阶段前向传播成功")
        print(f"输出形状: {output.shape if hasattr(output, 'shape') else type(output)}")
        
        # 检查模型参数
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"📊 总参数数量: {total_params:,}")
        print(f"📊 可训练参数数量: {trainable_params:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_stage2_config()
    if success:
        print("\n🎉 第二阶段配置测试通过！")
    else:
        print("\n💥 第二阶段配置测试失败！")
