python tools/train.py configs/pct_base_tokenizer.py --resume-from work_dirs/pct_base_tokenizer/epoch_10.pth

2025-03-29 11:05:17,707 - mmpose - INFO - Epoch(val) [11][397]  AP: 0.6485, AP .5: 0.9356, AP .75: 0.7294, AP (M): 0.6695, AP (L): 0.6373, AR: 0.7485, AR .5: 0.9643, AR .75: 0.8396, AR (M): 0.7482, AR (L): 0.7502
2025-03-29 11:23:21,564 - mmpose - INFO - Epoch(val) [12][397]  AP: 0.5561, AP .5: 0.9253, AP .75: 0.5888, AP (M): 0.5763, AP (L): 0.5486, AR: 0.6654, AR .5: 0.9594, AR .75: 0.7476, AR (M): 0.6651, AR (L): 0.6670
2025-03-29 11:41:25,771 - mmpose - INFO - Epoch(val) [13][397]  AP: 0.4716, AP .5: 0.8555, AP .75: 0.4776, AP (M): 0.4915, AP (L): 0.4668, AR: 0.5955, AR .5: 0.9144, AR .75: 0.6691, AR (M): 0.5919, AR (L): 0.6016
2025-03-29 11:59:25,669 - mmpose - INFO - Epoch(val) [14][397]  AP: 0.4774, AP .5: 0.8578, AP .75: 0.4818, AP (M): 0.4983, AP (L): 0.4685, AR: 0.6039, AR .5: 0.9177, AR .75: 0.6644, AR (M): 0.6025, AR (L): 0.6069
2025-03-29 12:17:26,495 - mmpose - INFO - Epoch(val) [15][397]  AP: 0.3192, AP .5: 0.8590, AP .75: 0.1046, AP (M): 0.3411, AP (L): 0.3077, AR: 0.4387, AR .5: 0.9178, AR .75: 0.2933, AR (M): 0.4414, AR (L): 0.4355

2025-03-31 11:15:35,949 - mmpose - INFO - Epoch(val) [16][397]  AP: 0.4225, AP .5: 0.8200, AP .75: 0.4020, AP (M): 0.4482, AP (L): 0.4068, AR: 0.5463, AR .5: 0.8952, AR .75: 0.6009, AR (M): 0.5478, AR (L): 0.5454
2025-03-31 11:33:40,066 - mmpose - INFO - Epoch(val) [17][397]  AP: 0.6851, AP .5: 0.9517, AP .75: 0.8202, AP (M): 0.6971, AP (L): 0.6885, AR: 0.7708, AR .5: 0.9734, AR .75: 0.8939, AR (M): 0.7658, AR (L): 0.7792
2025-03-31 11:51:42,715 - mmpose - INFO - Epoch(val) [18][397]  AP: 0.5956, AP .5: 0.9180, AP .75: 0.6392, AP (M): 0.6184, AP (L): 0.5868, AR: 0.7130, AR .5: 0.9506, AR .75: 0.7796, AR (M): 0.7132, AR (L): 0.7143
2025-03-31 12:09:41,973 - mmpose - INFO - Epoch(val) [19][397]  AP: 0.2831, AP .5: 0.7161, AP .75: 0.1689, AP (M): 0.3190, AP (L): 0.2573, AR: 0.4212, AR .5: 0.8297, AR .75: 0.3747, AR (M): 0.4281, AR (L): 0.4126
2025-03-31 12:27:44,245 - mmpose - INFO - Epoch(val) [20][397]  AP: 0.5890, AP .5: 0.9346, AP .75: 0.6642, AP (M): 0.6076, AP (L): 0.5815, AR: 0.6889, AR .5: 0.9614, AR .75: 0.7969, AR (M): 0.6878, AR (L): 0.6915
2025-03-31 12:45:44,618 - mmpose - INFO - Epoch(val) [21][397]  AP: 0.5859, AP .5: 0.9227, AP .75: 0.6654, AP (M): 0.6030, AP (L): 0.5813, AR: 0.6896, AR .5: 0.9567, AR .75: 0.7944, AR (M): 0.6870, AR (L): 0.6946
2025-03-31 13:03:43,522 - mmpose - INFO - Epoch(val) [22][397]  AP: 0.4052, AP .5: 0.9046, AP .75: 0.2368, AP (M): 0.4259, AP (L): 0.3948, AR: 0.5192, AR .5: 0.9454, AR .75: 0.4554, AR (M): 0.5148, AR (L): 0.5259
2025-03-31 13:21:44,547 - mmpose - INFO - Epoch(val) [23][397]  AP: 0.4904, AP .5: 0.9355, AP .75: 0.4560, AP (M): 0.5140, AP (L): 0.4741, AR: 0.5955, AR .5: 0.9611, AR .75: 0.6455, AR (M): 0.5911, AR (L): 0.6025
2025-03-31 13:39:46,592 - mmpose - INFO - Epoch(val) [24][397]  AP: 0.4066, AP .5: 0.9016, AP .75: 0.2377, AP (M): 0.4239, AP (L): 0.3967, AR: 0.5181, AR .5: 0.9438, AR .75: 0.4564, AR (M): 0.5158, AR (L): 0.5219

2025-04-01 10:43:10,530 - mmpose - INFO - Epoch(val) [25][397]  AP: 0.7058, AP .5: 0.9533, AP .75: 0.8252, AP (M): 0.7266, AP (L): 0.6998, AR: 0.7941, AR .5: 0.9740, AR .75: 0.8999, AR (M): 0.7966, AR (L): 0.7920
2025-04-01 11:01:21,492 - mmpose - INFO - Epoch(val) [26][397]  AP: 0.7005, AP .5: 0.9532, AP .75: 0.8335, AP (M): 0.7137, AP (L): 0.6987, AR: 0.7842, AR .5: 0.9747, AR .75: 0.9013, AR (M): 0.7813, AR (L): 0.7891
2025-04-01 11:19:38,036 - mmpose - INFO - Epoch(val) [27][397]  AP: 0.6469, AP .5: 0.9548, AP .75: 0.7736, AP (M): 0.6663, AP (L): 0.6398, AR: 0.7355, AR .5: 0.9747, AR .75: 0.8645, AR (M): 0.7326, AR (L): 0.7407
2025-04-01 11:38:05,369 - mmpose - INFO - Epoch(val) [28][397]  AP: 0.3676, AP .5: 0.8743, AP .75: 0.1752, AP (M): 0.3922, AP (L): 0.3512, AR: 0.4871, AR .5: 0.9266, AR .75: 0.3859, AR (M): 0.4897, AR (L): 0.4846
2025-04-01 11:56:58,741 - mmpose - INFO - Epoch(val) [29][397]  AP: 0.6967, AP .5: 0.9529, AP .75: 0.8038, AP (M): 0.7163, AP (L): 0.6890, AR: 0.7864, AR .5: 0.9737, AR .75: 0.8849, AR (M): 0.7824, AR (L): 0.7937
2025-04-01 12:15:56,139 - mmpose - INFO - Epoch(val) [30][397]  AP: 0.7168, AP .5: 0.9556, AP .75: 0.8534, AP (M): 0.7230, AP (L): 0.7193, AR: 0.7948, AR .5: 0.9753, AR .75: 0.9137, AR (M): 0.7884, AR (L): 0.8050
2025-04-01 18:37:06,456 - mmpose - INFO - Epoch(val) [31][397]  AP: 0.7489, AP .5: 0.9686, AP .75: 0.8867, AP (M): 0.7624, AP (L): 0.7456, AR: 0.8223, AR .5: 0.9813, AR .75: 0.9359, AR (M): 0.8208, AR (L): 0.8257
2025-04-01 18:55:35,643 - mmpose - INFO - Epoch(val) [32][397]  AP: 0.6997, AP .5: 0.9561, AP .75: 0.8362, AP (M): 0.7105, AP (L): 0.6976, AR: 0.7806, AR .5: 0.9781, AR .75: 0.9019, AR (M): 0.7742, AR (L): 0.7906
2025-04-01 19:14:41,752 - mmpose - INFO - Epoch(val) [33][397]  AP: 0.6760, AP .5: 0.9559, AP .75: 0.8204, AP (M): 0.6901, AP (L): 0.6745, AR: 0.7613, AR .5: 0.9772, AR .75: 0.8944, AR (M): 0.7597, AR (L): 0.7648
2025-04-01 19:33:30,508 - mmpose - INFO - Epoch(val) [34][397]  AP: 0.5751, AP .5: 0.9379, AP .75: 0.6475, AP (M): 0.5937, AP (L): 0.5644, AR: 0.6774, AR .5: 0.9646, AR .75: 0.7821, AR (M): 0.6713, AR (L): 0.6871
2025-04-01 19:51:54,914 - mmpose - INFO - Epoch(val) [35][397]  AP: 0.7825, AP .5: 0.9689, AP .75: 0.8889, AP (M): 0.7963, AP (L): 0.7786, AR: 0.8537, AR .5: 0.9808, AR .75: 0.9391, AR (M): 0.8516, AR (L): 0.8579              lr: 2.321e-03
2025-04-01 20:10:02,893 - mmpose - INFO - Epoch(val) [36][397]  AP: 0.7473, AP .5: 0.9693, AP .75: 0.8813, AP (M): 0.7611, AP (L): 0.7425, AR: 0.8205, AR .5: 0.9821, AR .75: 0.9309, AR (M): 0.8202, AR (L): 0.8221              lr: 2.061e-03
2025-04-01 20:28:29,313 - mmpose - INFO - Epoch(val) [37][397]  AP: 0.7861, AP .5: 0.9738, AP .75: 0.9224, AP (M): 0.7911, AP (L): 0.7882, AR: 0.8501, AR .5: 0.9888, AR .75: 0.9554, AR (M): 0.8435, AR (L): 0.8608              lr: 1.813e-03
2025-04-01 20:46:56,279 - mmpose - INFO - Epoch(val) [38][397]  AP: 0.7268, AP .5: 0.9707, AP .75: 0.8520, AP (M): 0.7398, AP (L): 0.7253, AR: 0.8072, AR .5: 0.9833, AR .75: 0.9155, AR (M): 0.8014, AR (L): 0.8170              lr: 1.577e-03
lr: 1.355e-03         2025-04-01 21:05:23,639 - mmpose - INFO - Epoch(val) [39][397]  AP: 0.8210, AP .5: 0.9754, AP .75: 0.9345, AP (M): 0.8347, AP (L): 0.8152, AR: 0.8807, AR .5: 0.9888, AR .75: 0.9624, AR (M): 0.8796, AR (L): 0.8843

lr: 1.148e-03         2025-04-02 14:48:19,589 - mmpose - INFO - Epoch(val) [40][397]  AP: 0.8277, AP .5: 0.9760, AP .75: 0.9363, AP (M): 0.8386, AP (L): 0.8248, AR: 0.8857, AR .5: 0.9895, AR .75: 0.9616, AR (M): 0.8826, AR (L): 0.8917
lr: 9.550e-04         2025-04-02 15:06:37,410 - mmpose - INFO - Epoch(val) [41][397]  AP: 0.8300, AP .5: 0.9866, AP .75: 0.9403, AP (M): 0.8429, AP (L): 0.8227, AR: 0.8866, AR .5: 0.9912, AR .75: 0.9662, AR (M): 0.8871, AR (L): 0.8874
lr: 7.785e-04         2025-04-02 15:24:52,236 - mmpose - INFO - Epoch(val) [42][397]  AP: 0.8561, AP .5: 0.9870, AP .75: 0.9523, AP (M): 0.8669, AP (L): 0.8547, AR: 0.9090, AR .5: 0.9913, AR .75: 0.9724, AR (M): 0.9068, AR (L): 0.9133
lr: 6.186e-04         2025-04-02 15:43:37,729 - mmpose - INFO - Epoch(val) [43][397]  AP: 0.8646, AP .5: 0.9868, AP .75: 0.9528, AP (M): 0.8733, AP (L): 0.8618, AR: 0.9141, AR .5: 0.9918, AR .75: 0.9732, AR (M): 0.9112, AR (L): 0.9195
lr: 4.760e-04         2025-04-02 16:01:58,633 - mmpose - INFO - Epoch(val) [44][397]  AP: 0.8107, AP .5: 0.9867, AP .75: 0.9188, AP (M): 0.8250, AP (L): 0.8028, AR: 0.8740, AR .5: 0.9909, AR .75: 0.9517, AR (M): 0.8721, AR (L): 0.8784
lr: 3.512e-04         2025-04-02 16:21:09,227 - mmpose - INFO - Epoch(val) [45][397]  AP: 0.8185, AP .5: 0.9744, AP .75: 0.9225, AP (M): 0.8262, AP (L): 0.8184, AR: 0.8802, AR .5: 0.9885, AR .75: 0.9580, AR (M): 0.8763, AR (L): 0.8876
lr: 2.448e-04         2025-04-02 16:39:40,219 - mmpose - INFO - Epoch(val) [46][397]  AP: 0.8930, AP .5: 0.9885, AP .75: 0.9578, AP (M): 0.9033, AP (L): 0.8895, AR: 0.9346, AR .5: 0.9932, AR .75: 0.9789, AR (M): 0.9330, AR (L): 0.9386
lr: 1.572e-04         2025-04-02 16:58:00,796 - mmpose - INFO - Epoch(val) [47][397]  AP: 0.8990, AP .5: 0.9880, AP .75: 0.9689, AP (M): 0.9052, AP (L): 0.8958, AR: 0.9376, AR .5: 0.9935, AR .75: 0.9805, AR (M): 0.9348, AR (L): 0.9436
lr: 8.866e-05         2025-04-02 17:16:08,327 - mmpose - INFO - Epoch(val) [48][397]  AP: 0.9107, AP .5: 0.9889, AP .75: 0.9708, AP (M): 0.9200, AP (L): 0.9046, AR: 0.9462, AR .5: 0.9942, AR .75: 0.9838, AR (M): 0.9453, AR (L): 0.9493
lr: 3.953e-05         2025-04-02 17:57:15,848 - mmpose - INFO - Epoch(val) [49][397]  AP: 0.9133, AP .5: 0.9880, AP .75: 0.9695, AP (M): 0.9221, AP (L): 0.9084, AR: 0.9479, AR .5: 0.9934, AR .75: 0.9821, AR (M): 0.9467, AR (L): 0.9516
lr: 9.966e-06         2025-04-02 18:16:21,228 - mmpose - INFO - Epoch(val) [50][397]  AP: 0.9147, AP .5: 0.9882, AP .75: 0.9713, AP (M): 0.9253, AP (L): 0.9097, AR: 0.9492, AR .5: 0.9932, AR .75: 0.9838, AR (M): 0.9488, AR (L): 0.9521

python tools/test.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/swin_base.pth --eval mAP --cfg-options data.test.data_cfg.use_gt_bbox=False 

 Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets= 20 ] =  0.778
 Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets= 20 ] =  0.913
 Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets= 20 ] =  0.84 
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets= 20 ] =  0.742
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets= 20 ] =  0.842
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 20 ] =  0.821
 Average Recall     (AR) @[ IoU=0.50      | area=   all | maxDets= 20 ] =  0.948
 Average Recall     (AR) @[ IoU=0.75      | area=   all | maxDets= 20 ] =  0.884
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets= 20 ] =  0.782
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets= 20 ] =  0.880
AP: 0.7775659042188489
AP (L): 0.8423639902145905
AP (M): 0.7422051092407987
AP .5: 0.9133520214079103
AP .75: 0.8489983199842993
AR: 0.8213161209068008
AR (L): 0.8801560758082496
AR (M): 0.7818901939360831
AR .5: 0.9477329974811083
AR .75: 0.8839735516372796

python tools/train.py configs/pct_base_tokenizer.py --gpu-ids 0
2025-04-10 17:49:44,340 - mmpose - INFO - Epoch(val) [4][397]   AP: 0.9710, AP .5: 0.9899, AP .75: 0.9881, AP (M): 0.9723, AP (L): 0.9704, AR: 0.9850, AR .5: 0.9970, AR .75: 0.9943, AR (M): 0.9846,

 Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets= 20 ] =  0.778
 Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets= 20 ] =  0.913
 Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets= 20 ] =  0.849
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets= 20 ] =  0.742
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets= 20 ] =  0.842
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 20 ] =  0.821
 Average Recall     (AR) @[ IoU=0.50      | area=   all | maxDets= 20 ] =  0.948
 Average Recall     (AR) @[ IoU=0.75      | area=   all | maxDets= 20 ] =  0.884
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets= 20 ] =  0.782
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets= 20 ] =  0.880
AP: 0.7775659042188489
AP (L): 0.8423639902145905
AP (M): 0.7422051092407987
AP .5: 0.9133520214079103
AP .75: 0.8489983199842993
AR: 0.8213161209068008
AR (L): 0.8801560758082496
AR (M): 0.7818901939360831
AR .5: 0.9477329974811083
AR .75: 0.8839735516372796


with_heatmap

第二阶段训练
python ./tools/train.py ./configs/pct_base_classifier.py --gpu-ids 0

python ./tools/train.py ./configs/pct_base_classifier.py --gpu-ids 0 --resume-from work_dirs/pct_base_classifier/epoch_10.pth

 Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets= 20 ] =  0.772
 Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets= 20 ] =  0.910
 Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets= 20 ] =  0.842
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets= 20 ] =  0.736
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets= 20 ] =  0.841
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 20 ] =  0.819
 Average Recall     (AR) @[ IoU=0.50      | area=   all | maxDets= 20 ] =  0.949
 Average Recall     (AR) @[ IoU=0.75      | area=   all | maxDets= 20 ] =  0.881
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets= 20 ] =  0.779
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets= 20 ] =  0.880
AP: 0.7720437256969466
AP (L): 0.840896789001495
AP (M): 0.7355056572266752
AP .5: 0.9098777970616386
AP .75: 0.8422463717689505
AR: 0.8192852644836272
AR (L): 0.8797473058342623
AR (M): 0.7785031412182464
AR .5: 0.9485201511335013
AR .75: 0.881139798488665


python tools/test.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/epoch_348.pth --eval mAP --cfg-options data.test.data_cfg.use_gt_bbox=True


<EMAIL>
1194357826hyj@

2025-07-26 22:47:19,385 - mmpose - INFO - Best AP is 0.7950 at 282 epoch.
2025-07-26 22:47:19,385 - mmpose - INFO - Epoch(val) [282][199]	AP: 0.7950, AP .5: 0.9361, AP .75: 0.8714, AP (M): 0.7677, AP (L): 0.8369, AR: 0.8183, AR .5: 0.9460, AR .75: 0.8819, AR (M): 0.7891, AR (L): 0.8645

 Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets= 20 ] =  0.773
 Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets= 20 ] =  0.909
 Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets= 20 ] =  0.843
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets= 20 ] =  0.739
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets= 20 ] =  0.843
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 20 ] =  0.821
 Average Recall     (AR) @[ IoU=0.50      | area=   all | maxDets= 20 ] =  0.948
 Average Recall     (AR) @[ IoU=0.75      | area=   all | maxDets= 20 ] =  0.883
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets= 20 ] =  0.780
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets= 20 ] =  0.882
AP: 0.7726739880808727
AP (L): 0.8428061718371764
AP (M): 0.7391630212244243
AP .5: 0.9094593283943951
AP .75: 0.8433732508526404
AR: 0.8210170025188915
AR (L): 0.881716833890747
AR (M): 0.780142037694619
AR .5: 0.948205289672544
AR .75: 0.8827141057934509
