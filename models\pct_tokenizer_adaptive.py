# --------------------------------------------------------
# Adaptive PCT Tokenizer for different datasets
# 自适应不同数据集关键点数量的tokenizer
# 保留原始tokenizer的兼容性，同时支持MPII等其他数据集
# --------------------------------------------------------

import os
import torch
import torch.nn as nn
import torch.nn.functional as F

from mmpose.models.builder import build_loss
from mmpose.models.builder import HEADS
from timm.models.layers import trunc_normal_

from .modules import MixerLayer
from .estimate_invisible_points import InvisiblePointEstimator

@HEADS.register_module()
class GroupedL2LossAdaptive(nn.Module):
    """自适应的分组L2损失，支持不同数量的关键点"""
    def __init__(self, num_joints=17, dataset_type='COCO'):
        super().__init__()
        self.num_joints = num_joints
        self.dataset_type = dataset_type
        
        if dataset_type == 'COCO' and num_joints == 17:
            # COCO 17个关键点分组
            self.head_indices = [0, 1, 2, 3, 4]
            self.arm_indices = [5, 6, 7, 8, 9, 10]
            self.leg_indices = [11, 12, 13, 14, 15, 16]
        elif dataset_type == 'MPII' and num_joints == 16:
            # MPII 16个关键点分组
            self.head_indices = [6, 7, 8, 9]  # pelvis, thorax, upper_neck, head_top
            self.arm_indices = [10, 11, 12, 13, 14, 15]  # wrists, elbows, shoulders
            self.leg_indices = [0, 1, 2, 3, 4, 5]  # ankles, knees, hips
        else:
            # 默认均匀分组
            third = num_joints // 3
            self.head_indices = list(range(third))
            self.arm_indices = list(range(third, 2*third))
            self.leg_indices = list(range(2*third, num_joints))
        
        self.arm_weight = 2.0
        self.leg_weight = 2.0

    def forward(self, pred, gt):
        # 提取可见点信息 (完全按照原始实现)
        visible = gt[:, :, -1].unsqueeze(-1)

        # 计算头部关节点的损失 (加开根号)
        head_loss = torch.sqrt(F.mse_loss(pred[:, self.head_indices, :], gt[:, self.head_indices, :-1], reduction='none') + 1e-6)
        head_loss_visible = (head_loss * visible[:, self.head_indices, :]).mean()

        # 计算手臂关节点的损失 (加开根号)
        arm_loss = torch.sqrt(F.mse_loss(pred[:, self.arm_indices, :], gt[:, self.arm_indices, :-1], reduction='none') + 1e-6)
        arm_loss_visible = (arm_loss * visible[:, self.arm_indices, :]).mean() * self.arm_weight

        # 计算腿部关节点的损失 (加开根号)
        leg_loss = torch.sqrt(F.mse_loss(pred[:, self.leg_indices, :], gt[:, self.leg_indices, :-1], reduction='none') + 1e-6)
        leg_loss_visible = (leg_loss * visible[:, self.leg_indices, :]).mean() * self.leg_weight

        return head_loss_visible + arm_loss_visible + leg_loss_visible

@HEADS.register_module()
class SELayerAdaptive(nn.Module):
    """自适应的SE层，支持不同数量的关键点"""
    def __init__(self, channel, reduction=16):
        super().__init__()
        self.channel = channel
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channel, channel // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channel // reduction, channel, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)

@HEADS.register_module()
class PCT_Tokenizer_Adaptive(nn.Module):
    """自适应的PCT Tokenizer，支持不同数量的关键点"""
    
    def __init__(self,
                 stage_pct,
                 tokenizer=None,
                 num_joints=17,  # 默认COCO的17个关键点
                 dataset_type='COCO',  # 数据集类型
                 guide_ratio=0,
                 guide_channels=0):
        super().__init__()
        
        self.stage_pct = stage_pct
        self.guide_ratio = guide_ratio
        self.num_joints = num_joints
        self.dataset_type = dataset_type
        self.tokenizer_config = tokenizer  # 保存配置用于损失函数

        if tokenizer is not None:
            self.drop_rate = tokenizer['encoder']['drop_rate']
            self.enc_num_blocks = tokenizer['encoder']['num_blocks']
            self.enc_hidden_dim = tokenizer['encoder']['hidden_dim']
            self.enc_token_inter_dim = tokenizer['encoder']['token_inter_dim']
            self.enc_hidden_inter_dim = tokenizer['encoder']['hidden_inter_dim']
            self.enc_dropout = tokenizer['encoder']['dropout']
            
            self.dec_num_blocks = tokenizer['decoder']['num_blocks']
            self.dec_hidden_dim = tokenizer['decoder']['hidden_dim']
            self.dec_token_inter_dim = tokenizer['decoder']['token_inter_dim']
            self.dec_hidden_inter_dim = tokenizer['decoder']['hidden_inter_dim']
            self.dec_dropout = tokenizer['decoder']['dropout']
            
            self.token_num = tokenizer['codebook']['token_num']
            self.token_dim = tokenizer['codebook']['token_dim']
            self.token_class_num = tokenizer['codebook']['token_class_num']
            self.ema_decay = tokenizer['codebook']['ema_decay']
            
            self.mask_noise_scale = 0.1
            
            # 构建网络层
            self._build_layers()
            
            # 构建损失函数
            self.loss = build_loss(tokenizer['loss_keypoint'])
    
    def _build_layers(self):
        """构建网络层，自适应关键点数量"""
        
        # 不可见点估计器
        self.invisible_estimator = InvisiblePointEstimator()
        
        # 编码器层 - 保持原始维度
        self.start_embed = nn.Linear(2, self.enc_hidden_dim)
        
        # 位置编码 - 自适应关键点数量，维度与start_embed输出一致
        self.learnable_positional_encoding = nn.Parameter(
            torch.randn(self.num_joints, int(self.enc_hidden_dim * (1 - self.guide_ratio))) * 0.02
        )
        
        # 编码器
        self.encoder = nn.ModuleList([
            MixerLayer(
                self.enc_hidden_dim,  # 特征维度
                self.enc_hidden_inter_dim,  # 隐藏层维度
                self.num_joints,  # token维度（关键点数量）
                self.enc_token_inter_dim,  # token中间维度
                self.enc_dropout  # dropout比例
            ) for _ in range(self.enc_num_blocks)
        ])
        
        # Token映射层 - 完全按照原始流程
        self.token_mlp = nn.Linear(self.num_joints, self.token_num)
        self.feature_embed = nn.Linear(self.enc_hidden_dim, self.token_dim)

        # Codebook - 改为register_buffer以支持EMA
        self.register_buffer('codebook',
            torch.empty(self.token_class_num, self.token_dim))
        self.codebook.data.normal_()
        self.register_buffer('ema_cluster_size',
            torch.zeros(self.token_class_num))
        self.register_buffer('ema_w',
            torch.empty(self.token_class_num, self.token_dim))
        self.ema_w.data.normal_()

        # 解码器Token映射 - 自适应关键点数量
        self.decoder_token_mlp = nn.Linear(self.token_num, self.num_joints)
        self.decoder_start = nn.Linear(self.token_dim, self.dec_hidden_dim)
        
        # 解码器
        self.decoder = nn.ModuleList([
            MixerLayer(
                self.dec_hidden_dim,  # 特征维度
                self.dec_hidden_inter_dim,  # 隐藏层维度
                self.num_joints,  # token维度（关键点数量）
                self.dec_token_inter_dim,  # token中间维度
                self.dec_dropout  # dropout比例
            ) for _ in range(self.dec_num_blocks)
        ])

        # 解码器层归一化 - 按照原始实现
        self.decoder_layer_norm = nn.LayerNorm(self.dec_hidden_dim)
        
        # 位置编码
        self.position_encoding = nn.Sequential(
            nn.Linear(2, 256),
            nn.ReLU(),
            nn.Linear(256, 512),
            nn.ReLU()
        )
        self.position_encoding_proj = nn.Linear(512, int(self.enc_hidden_dim * (1 - self.guide_ratio)))
        
        # 恢复嵌入
        self.recover_embed = nn.Linear(self.dec_hidden_dim, 2)
        
        # SE模块 - 自适应关键点数量
        self.se = SELayerAdaptive(channel=self.num_joints)
        
        # 分组损失函数 - 自适应关键点数量
        self.grouped_l2_loss = GroupedL2LossAdaptive(
            num_joints=self.num_joints,
            dataset_type=self.dataset_type
        )

        # 主损失函数 - 使用配置文件中定义的损失（Smooth L1 + VQ损失）
        if self.tokenizer_config is not None and 'loss_keypoint' in self.tokenizer_config:
            self.loss = build_loss(self.tokenizer_config['loss_keypoint'])
        else:
            self.loss = None

        # 特征嵌入 - 完全按照原始实现的维度分配
        if self.guide_ratio > 0:
            # 图像特征嵌入：guide_channels -> enc_hidden_dim * guide_ratio
            self.start_img_embed = nn.Linear(
                1024, int(self.enc_hidden_dim * self.guide_ratio))

        # 关键点坐标嵌入：2 -> enc_hidden_dim * (1-guide_ratio)
        self.start_embed = nn.Linear(
            2, int(self.enc_hidden_dim * (1 - self.guide_ratio)))
    
    def position_encoding_func(self, joints_coord):
        """位置编码函数"""
        # 使用可学习的位置编码
        bs = joints_coord.shape[0]
        pos_encoding = self.learnable_positional_encoding.unsqueeze(0).repeat(bs, 1, 1)
        
        # 结合坐标信息
        coord_encoding = self.position_encoding(joints_coord)
        coord_encoding = self.position_encoding_proj(coord_encoding)
        
        # 组合编码
        combined_encoding = pos_encoding + coord_encoding
        
        return combined_encoding
    
    def forward(self, joints, joints_feature, cls_logits, train=True):
        """前向传播，自适应关键点数量"""

        if train or self.stage_pct == "tokenizer":
            if joints is None:
                # 在验证阶段，如果没有关键点信息，创建虚拟的关键点用于前向传播
                if not train and self.stage_pct == "tokenizer":
                    # 创建虚拟关键点 [batch_size, num_joints, 3]
                    batch_size = cls_logits.shape[0] if cls_logits is not None else 1
                    joints = torch.zeros(batch_size, self.num_joints, 3, device=cls_logits.device if cls_logits is not None else 'cpu')
                    joints[:, :, 2] = 1.0  # 设置可见性为1
                else:
                    # 训练阶段没有关键点信息，返回占位符
                    return None, None, None

            # 获取关键点坐标和可见性
            joints_coord, joints_visible, bs = (
                joints[:,:,:-1],
                joints[:,:,-1].bool(),
                joints.shape[0]
            )
            
            # 验证关键点数量
            actual_num_joints = joints.shape[1]
            if actual_num_joints != self.num_joints:
                raise ValueError(f"Expected {self.num_joints} joints, got {actual_num_joints}")
            
            # 使用不可见点估计器
            coords_for_embedding = self.invisible_estimator.estimate_invisible(
                joints_coord.clone(),
                joints_visible.clone()
            )
            
            # 位置编码
            encode_feat = self.start_embed(coords_for_embedding) + self.position_encoding_func(joints_coord)
            
            if self.guide_ratio > 0:
                # 图像特征处理 - 按照原始实现，直接使用关键点数量
                joints_feature = joints_feature.view(bs, self.num_joints, 32, 32)
                joints_feature = self.se(joints_feature)
                joints_feature = joints_feature.view(bs, self.num_joints, -1)
                encode_img_feat = self.start_img_embed(joints_feature)
                # 完全按照原始实现：使用torch.cat拼接特征
                encode_feat = torch.cat((encode_feat, encode_img_feat), dim=2)
            
            # 掩码策略
            if train and self.stage_pct == "tokenizer":
                rand_mask_ind = torch.rand(
                    joints_visible.shape, device=joints.device) > self.drop_rate
                joints_visible = torch.logical_and(rand_mask_ind, joints_visible) 
            
            w = joints_visible.unsqueeze(-1).type_as(encode_feat)
            noise = torch.randn_like(encode_feat) * self.mask_noise_scale
            encode_feat = encode_feat * w + noise * (1 - w)
            
            # 编码器
            for encoder_layer in self.encoder:
                encode_feat = encoder_layer(encode_feat)
            
            # Codebook量化 - 完全按照原始流程
            encode_feat = encode_feat.transpose(2, 1)
            encode_feat = self.token_mlp(encode_feat).transpose(2, 1)
            encode_feat = self.feature_embed(encode_feat).flatten(0, 1)

            distances = torch.sum(encode_feat**2, dim=1, keepdim=True) \
                + torch.sum(self.codebook**2, dim=1) \
                - 2 * torch.matmul(encode_feat, self.codebook.t())

            encoding_indices = torch.argmin(distances, dim=1)
            encodings = torch.zeros(
                encoding_indices.shape[0], self.token_class_num, device=joints.device)
            encodings.scatter_(1, encoding_indices.unsqueeze(1), 1)
        else:
            bs = cls_logits.shape[0] // self.token_num
            encoding_indices = None

        if self.stage_pct == "classifier":
            part_token_feat = torch.matmul(cls_logits, self.codebook)
        else:
            part_token_feat = torch.matmul(encodings, self.codebook)

        # EMA更新机制 - 完全按照原始流程
        if train and self.stage_pct == "tokenizer":
            # Updating Codebook using EMA
            dw = torch.matmul(encodings.t(), encode_feat.detach())

            # 直接使用本地数据，无需分布式同步
            sync_encodings, sync_dw = encodings, dw

            self.ema_cluster_size = self.ema_cluster_size * self.ema_decay + \
                                    (1 - self.ema_decay) * torch.sum(sync_encodings, 0)

            n = torch.sum(self.ema_cluster_size.data)
            self.ema_cluster_size = (
                (self.ema_cluster_size + 1e-5)
                / (n + self.token_class_num * 1e-5) * n)

            self.ema_w = self.ema_w * self.ema_decay + (1 - self.ema_decay) * sync_dw
            self.codebook = self.ema_w / self.ema_cluster_size.unsqueeze(1)
            e_latent_loss = F.mse_loss(part_token_feat.detach(), encode_feat)
            part_token_feat = encode_feat + (part_token_feat - encode_feat).detach()
        else:
            e_latent_loss = None

        # 解码器 - 完全按照原始流程
        part_token_feat = part_token_feat.view(bs, -1, self.token_dim)

        part_token_feat = part_token_feat.transpose(2, 1)
        part_token_feat = self.decoder_token_mlp(part_token_feat).transpose(2, 1)
        decode_feat = self.decoder_start(part_token_feat)

        # 解码器 - 完全按照原始实现
        for decoder_layer in self.decoder:
            decode_feat = decoder_layer(decode_feat)
        decode_feat = self.decoder_layer_norm(decode_feat)

        # 恢复关键点 - 在所有decoder层之后
        recovered_joints = self.recover_embed(decode_feat)

        return recovered_joints, encoding_indices, e_latent_loss

    def get_loss(self, output_joints, joints, e_latent_loss):
        """计算损失（自适应版本）"""
        losses = dict()

        # 完全按照原始tokenizer的损失计算
        kpt_loss, e_latent_loss = self.loss(output_joints, joints, e_latent_loss)

        # 添加分组损失 (完全按照原始实现)
        grouped_loss = self.grouped_l2_loss(output_joints, joints)

        # 加权平均 (完全按照原始实现)
        total_loss = 0.8 * kpt_loss + 0.2 * grouped_loss

        losses['joint_loss'] = total_loss
        losses['e_latent_loss'] = e_latent_loss

        return losses

    def init_weights(self, pretrained=None):
        """初始化权重"""
        if pretrained is not None:
            print(f"Loading adaptive tokenizer weights from {pretrained}")
            # 这里可以实现权重加载逻辑，支持不同关键点数量的权重转换
            pass
