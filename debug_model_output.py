#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试模型输出，检查预测坐标是否合理
"""

import torch
import numpy as np
from mmcv import Config
import models
from mmpose.models import build_posenet
import matplotlib.pyplot as plt

def debug_model_predictions():
    print("🔍 调试模型输出...")
    
    # 加载配置和模型
    cfg = Config.fromfile('configs/pct_mpii_test_stage1.py')
    model = build_posenet(cfg.model)
    
    # 加载权重
    checkpoint = torch.load('work_dirs/stage1/epoch_30.pth', map_location='cpu')
    model.load_state_dict(checkpoint['state_dict'], strict=False)
    model.eval()
    
    # 创建测试输入
    batch_size = 2
    img = torch.randn(batch_size, 3, 256, 256)
    joints = torch.randn(batch_size, 16, 3) * 100 + 128  # 模拟MPII坐标范围
    joints_visible = torch.ones(batch_size, 16, 3)
    
    img_metas = [{
        'image_file': f'test_{i}.jpg',
        'bbox_id': i,
        'center': [128, 128],
        'scale': [1.0, 1.0],
        'rotation': 0,
        'bbox_score': 1.0,
        'joints_3d': joints[i].numpy(),
        'joints_3d_visible': joints_visible[i].numpy()
    } for i in range(batch_size)]
    
    print("📊 输入数据统计:")
    print(f"图像形状: {img.shape}")
    print(f"真实关键点范围: [{joints.min():.1f}, {joints.max():.1f}]")
    print(f"真实关键点均值: {joints.mean():.1f}")
    
    # 模型推理
    with torch.no_grad():
        output = model(img, joints_3d=joints, joints_3d_visible=joints_visible,
                      img_metas=img_metas, return_loss=False)

    print("📊 模型输出统计:")
    print(f"输出类型: {type(output)}")
    if isinstance(output, dict):
        print(f"输出键: {list(output.keys())}")
        # 通常关键点预测在'preds'键中
        if 'preds' in output:
            preds = output['preds']
        else:
            # 或者直接是第一个值
            preds = list(output.values())[0]
    else:
        preds = output

    # 转换为tensor以便统一处理
    if isinstance(preds, np.ndarray):
        preds_tensor = torch.from_numpy(preds)
    else:
        preds_tensor = preds

    print(f"预测形状: {preds.shape}")
    print(f"预测坐标范围: [{preds.min():.1f}, {preds.max():.1f}]")
    print(f"预测坐标均值: {preds.mean():.1f}")
    print(f"预测坐标标准差: {preds.std():.1f}")

    # 检查预测是否合理
    if preds.min() < -1000 or preds.max() > 1000:
        print("⚠️ 预测坐标范围异常！可能存在数值问题")

    if np.isnan(preds).any():
        print("❌ 预测包含NaN值！")

    if np.isinf(preds).any():
        print("❌ 预测包含无穷大值！")

    # 计算与真实值的距离 (只使用x,y坐标)
    pred_coords = preds_tensor[:, :, :2]  # 只取x,y坐标
    true_coords = joints[:, :, :2]
    
    distances = torch.sqrt(torch.sum((pred_coords - true_coords)**2, dim=2))
    print(f"📏 预测与真实的平均距离: {distances.mean():.1f} 像素")
    print(f"📏 距离范围: [{distances.min():.1f}, {distances.max():.1f}] 像素")
    
    # 检查是否所有预测都相同（可能模型没有学到有用信息）
    if torch.allclose(pred_coords[0], pred_coords[1], atol=1e-3):
        print("⚠️ 不同样本的预测几乎相同，模型可能没有学到有用信息")
    
    # 可视化第一个样本的预测
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    true_xy = true_coords[0].numpy()
    plt.scatter(true_xy[:, 0], true_xy[:, 1], c='blue', label='真实坐标', s=50)
    plt.title('真实关键点')
    plt.xlabel('X坐标')
    plt.ylabel('Y坐标')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    pred_xy = pred_coords[0].numpy()
    plt.scatter(pred_xy[:, 0], pred_xy[:, 1], c='red', label='预测坐标', s=50)
    plt.title('预测关键点')
    plt.xlabel('X坐标')
    plt.ylabel('Y坐标')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('model_prediction_debug.png', dpi=150, bbox_inches='tight')
    print("📊 可视化结果已保存到: model_prediction_debug.png")
    
    return {
        'pred_range': [preds.min(), preds.max()],
        'pred_mean': preds.mean(),
        'avg_distance': distances.mean().item(),
        'has_nan': np.isnan(preds).any(),
        'has_inf': np.isinf(preds).any()
    }

if __name__ == '__main__':
    results = debug_model_predictions()
    print("\n🎯 调试结果总结:")
    for key, value in results.items():
        print(f"  {key}: {value}")
