#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型初始化
"""

import torch
from mmcv import Config
import models  # 确保导入我们的自定义模型
from mmpose.models import build_posenet

def test_model_init():
    print("🔍 测试模型初始化...")
    
    try:
        # 加载配置
        cfg = Config.fromfile('configs/pct_mpii_tokenizer_stage1.py')
        print("✅ 配置文件加载成功")
        
        # 构建模型
        model = build_posenet(cfg.model)
        print("✅ 模型构建成功")
        
        # 测试前向传播
        batch_size = 2
        img = torch.randn(batch_size, 3, 256, 256)
        joints = torch.randn(batch_size, 16, 3)  # MPII 16个关键点
        joints_visible = torch.ones(batch_size, 16, 3)  # 所有关键点都可见
        img_metas = [{
            'image_file': 'test.jpg',
            'bbox_id': i,
            'center': [128, 128],  # 图像中心
            'scale': [1.0, 1.0],   # 缩放比例
            'rotation': 0,         # 旋转角度
            'bbox_score': 1.0      # bbox置信度
        } for i in range(batch_size)]

        print("🔍 测试前向传播...")
        model.eval()
        with torch.no_grad():
            output = model(img, joints_3d=joints, joints_3d_visible=joints_visible, img_metas=img_metas, return_loss=False)
        print("✅ 前向传播成功")
        print(f"输出形状: {output.shape if hasattr(output, 'shape') else type(output)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_model_init()
    if success:
        print("\n🎉 模型初始化测试通过！")
    else:
        print("\n💥 模型初始化测试失败！")
