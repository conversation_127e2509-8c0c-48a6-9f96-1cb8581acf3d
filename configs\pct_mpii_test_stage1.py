# configs/pct_mpii_test_stage1.py - 第一阶段tokenizer测试配置
_base_ = ['./mpii.py']

# MPII数据集配置 - 16个关键点
channel_cfg = dict(
    num_output_channels=16,
    dataset_joints=16,
    dataset_channel=[
        [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
    ],
    inference_channel=[
        0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15
    ]
)

data_cfg = dict(
    image_size=[256, 256],
    heatmap_size=[64, 64],
    num_output_channels=channel_cfg['num_output_channels'],
    num_joints=channel_cfg['dataset_joints'],
    dataset_channel=channel_cfg['dataset_channel'],
    inference_channel=channel_cfg['inference_channel'],
    soft_nms=False,
    nms_thr=1.0,
    oks_thr=0.9,
    vis_thr=0.2,
    use_gt_bbox=True,  # MPII使用ground truth bbox
    det_bbox_thr=0.0,
)

# 测试时的评估配置
evaluation = dict(
    metric='PCKh',
    metric_options=dict(thr=0.5)  # 设置PCKh阈值为0.5
)

# 第一阶段tokenizer测试pipeline
test_pipeline = [
    dict(type='LoadImageFromFile'),
    dict(type='TopDownGetBboxCenterScale', padding=1.12),
    dict(type='TopDownAffine'),
    dict(type='ToTensor'),
    dict(
        type='NormalizeTensor',
        mean=[0.485, 0.456, 0.406],
        std=[0.229, 0.224, 0.225]
    ),
    dict(
        type='Collect',
        keys=['img', 'joints_3d', 'joints_3d_visible'],  # 包含关键点信息用于PCKh计算
        meta_keys=[
            'image_file', 'joints_3d', 'joints_3d_visible', 'center', 'scale', 
            'rotation', 'bbox_score', 'flip_pairs'
        ],
    ),
]

# 第一阶段模型配置 (tokenizer阶段)
model = dict(
    type='PCT',
    pretrained='weights/heatmap/swin_base.pth',
    backbone=dict(
        type='SwinV2TransformerRPE2FC',
        embed_dim=128,
        depths=[2, 2, 18, 2],
        num_heads=[4, 8, 16, 32],
        window_size=[16, 16, 16, 8],
        pretrain_window_size=[12, 12, 12, 6],
        ape=False,
        drop_path_rate=0.3,
        patch_norm=True,
        use_checkpoint=True,
        rpe_interpolation='geo',
        use_shift=[True, True, False, False],
        relative_coords_table_type='norm8_log_bylayer',
        attn_type='cosine_mh',
        rpe_output_type='sigmoid',
        postnorm=True,
        mlp_type='normal',
        out_indices=(0, 1, 2, 3),
        frozen_stages=5,
    ),
    keypoint_head=dict(
        type='PCT_Head_Adaptive',  # 使用自适应Head
        stage_pct='tokenizer',  # 第一阶段
        in_channels=1024,
        image_size=data_cfg['image_size'],
        num_joints=channel_cfg['num_output_channels'],
        dataset_type='MPII',  # 指定数据集类型
        use_adaptive_tokenizer=True,  # 使用自适应tokenizer
        loss_keypoint=dict(
            type='Classifer_loss',
            token_loss=1.0,
            joint_loss=1.0
        ),
        cls_head=dict(
            conv_num_blocks=2,
            conv_channels=256,
            dilation=1,
            num_blocks=4,
            hidden_dim=64,
            token_inter_dim=64,
            hidden_inter_dim=256,
            dropout=0.0
        ),
        tokenizer=dict(
            guide_ratio=0.5,
            ckpt="",  # 测试时不需要加载tokenizer权重
            heatmap_size=[64, 64],
            e_loss_w_for_vq=15.0,
            encoder=dict(
                drop_rate=0.2,
                num_blocks=4,
                hidden_dim=512,
                token_inter_dim=64,
                hidden_inter_dim=512,
                dropout=0.0,
            ),
            decoder=dict(
                num_blocks=1,
                hidden_dim=32,   # 恢复原始的渐进式降维设计
                token_inter_dim=64,
                hidden_inter_dim=64,  # 恢复原始设计
                dropout=0.0,
            ),
            codebook=dict(
                token_num=34,
                token_dim=512,
                token_class_num=2048,
                ema_decay=0.9,
            ),
            loss_keypoint=dict(
                type='Tokenizer_loss',
                joint_loss_w=1.0, 
                e_loss_w=15.0,
                beta=0.05,
            )
        )
    ),
    test_cfg=dict(
        flip_test=True,
        dataset_name='MPII'
    )
)

# 数据配置
data_root = 'data/MPII/mpii_human_pose_v1'
data = dict(
    samples_per_gpu=32,
    workers_per_gpu=2,
    test_dataloader=dict(samples_per_gpu=32),
    test=dict(
        type='TopDownMpiiDataset',
        ann_file=f'{data_root}/annotations/valid.json',
        img_prefix=f'{data_root}/images/',
        data_cfg=data_cfg,
        pipeline=test_pipeline,
        dataset_info={{_base_.dataset_info}},
    )
)
