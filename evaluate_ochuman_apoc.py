#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCHuman APoc评估脚本
只计算遮挡关键点(visibility=1)的精度
"""

import json
import numpy as np
import argparse
from collections import defaultdict

def compute_oks(pred_kpts, gt_kpts, gt_vis, area, sigmas):
    """计算OKS (Object Keypoint Similarity)"""
    # COCO关键点的sigma值
    if sigmas is None:
        sigmas = np.array([.26, .25, .25, .35, .35, .79, .79, .72, .72, .62, .62, 1.07, 1.07, .87, .87, .89, .89]) / 10.0
    
    vars = (sigmas * 2) ** 2
    k = len(pred_kpts) // 3  # 关键点数量
    
    # 计算距离
    dx = pred_kpts[0::3] - gt_kpts[0::3]
    dy = pred_kpts[1::3] - gt_kpts[1::3]
    e = (dx**2 + dy**2) / vars / (area + np.spacing(1)) / 2
    
    # 只考虑可见的关键点
    e = e[gt_vis > 0]
    
    if len(e) == 0:
        return 0
    
    return np.sum(np.exp(-e)) / len(e)

def evaluate_apoc(pred_file, gt_file):
    """评估APoc指标"""
    
    # 加载预测结果
    with open(pred_file, 'r') as f:
        predictions = json.load(f)
    
    # 加载ground truth
    with open(gt_file, 'r') as f:
        gt_data = json.load(f)
    
    # 构建GT字典
    gt_dict = {}
    for ann in gt_data['annotations']:
        img_id = ann['image_id']
        if img_id not in gt_dict:
            gt_dict[img_id] = []
        gt_dict[img_id].append(ann)
    
    print("🔍 OCHuman APoc评估")
    print("=" * 50)
    print(f"预测结果数量: {len(predictions)}")
    print(f"GT标注数量: {len(gt_data['annotations'])}")
    
    # 统计遮挡关键点
    total_occluded_kpts = 0
    total_predictions = 0
    
    # 按IoU阈值计算AP
    iou_thresholds = np.arange(0.5, 1.0, 0.05)
    ap_scores = []
    
    for iou_thr in iou_thresholds:
        matches = []
        
        for pred in predictions:
            img_id = pred['image_id']
            pred_kpts = np.array(pred['keypoints'])
            pred_score = pred.get('score', 1.0)
            
            if img_id not in gt_dict:
                continue
            
            # 找到最佳匹配的GT
            best_oks = 0
            best_gt = None
            
            for gt_ann in gt_dict[img_id]:
                gt_kpts = np.array(gt_ann['keypoints'])
                gt_area = gt_ann['area']
                
                # 提取可见性信息
                gt_vis = gt_kpts[2::3]
                
                # 只考虑有遮挡关键点的样本
                occluded_mask = (gt_vis == 1)  # visibility=1表示遮挡但可见
                if not np.any(occluded_mask):
                    continue
                
                # 计算OKS
                oks = compute_oks(pred_kpts, gt_kpts, gt_vis, gt_area, None)
                
                if oks > best_oks:
                    best_oks = oks
                    best_gt = gt_ann
            
            if best_gt is not None:
                gt_vis = np.array(best_gt['keypoints'])[2::3]
                occluded_mask = (gt_vis == 1)
                
                if np.any(occluded_mask):
                    # 只计算遮挡关键点的OKS
                    occluded_oks = compute_oks_occluded_only(
                        pred_kpts, np.array(best_gt['keypoints']), 
                        gt_vis, best_gt['area'], occluded_mask
                    )
                    
                    matches.append({
                        'oks': occluded_oks,
                        'score': pred_score,
                        'matched': occluded_oks >= iou_thr
                    })
                    
                    total_occluded_kpts += np.sum(occluded_mask)
                    total_predictions += 1
        
        # 计算AP
        if len(matches) > 0:
            matches.sort(key=lambda x: x['score'], reverse=True)
            
            tp = np.array([m['matched'] for m in matches])
            fp = 1 - tp
            
            tp_cumsum = np.cumsum(tp)
            fp_cumsum = np.cumsum(fp)
            
            recalls = tp_cumsum / len(matches) if len(matches) > 0 else np.array([0])
            precisions = tp_cumsum / (tp_cumsum + fp_cumsum)
            
            # 计算AP (使用11点插值)
            ap = compute_ap(recalls, precisions)
            ap_scores.append(ap)
        else:
            ap_scores.append(0.0)
    
    # 计算最终APoc
    apoc = np.mean(ap_scores)
    ap50 = ap_scores[0] if len(ap_scores) > 0 else 0.0  # IoU=0.5
    ap75 = ap_scores[5] if len(ap_scores) > 5 else 0.0  # IoU=0.75
    
    print(f"\n📊 APoc评估结果:")
    print(f"APoc (IoU=0.5:0.95): {apoc:.3f}")
    print(f"APoc@0.5: {ap50:.3f}")
    print(f"APoc@0.75: {ap75:.3f}")
    print(f"总遮挡关键点数: {total_occluded_kpts}")
    print(f"有效预测数: {total_predictions}")
    
    return apoc, ap50, ap75

def compute_oks_occluded_only(pred_kpts, gt_kpts, gt_vis, area, occluded_mask):
    """只计算遮挡关键点的OKS"""
    sigmas = np.array([.26, .25, .25, .35, .35, .79, .79, .72, .72, .62, .62, 1.07, 1.07, .87, .87, .89, .89]) / 10.0
    vars = (sigmas * 2) ** 2
    
    # 只考虑遮挡的关键点
    pred_x = pred_kpts[0::3][occluded_mask]
    pred_y = pred_kpts[1::3][occluded_mask]
    gt_x = gt_kpts[0::3][occluded_mask]
    gt_y = gt_kpts[1::3][occluded_mask]
    vars_occluded = vars[occluded_mask]
    
    if len(pred_x) == 0:
        return 0
    
    dx = pred_x - gt_x
    dy = pred_y - gt_y
    e = (dx**2 + dy**2) / vars_occluded / (area + np.spacing(1)) / 2
    
    return np.sum(np.exp(-e)) / len(e)

def compute_ap(recalls, precisions):
    """计算AP (11点插值)"""
    # 11点插值
    ap = 0
    for t in np.arange(0, 1.1, 0.1):
        if np.sum(recalls >= t) == 0:
            p = 0
        else:
            p = np.max(precisions[recalls >= t])
        ap += p / 11
    return ap

def main():
    parser = argparse.ArgumentParser(description='Evaluate APoc on OCHuman dataset')
    parser.add_argument('--pred', 
                       default='work_dirs/coco_on_ochuman_direct_test.json',
                       help='预测结果文件')
    parser.add_argument('--gt', 
                       default='data/OCHuman/ochuman_coco_format_val_range_0.00_1.00.json',
                       help='Ground truth文件')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.pred):
        print(f"❌ 预测文件不存在: {args.pred}")
        return 1
    
    if not os.path.exists(args.gt):
        print(f"❌ GT文件不存在: {args.gt}")
        return 1
    
    # 评估APoc
    apoc, ap50, ap75 = evaluate_apoc(args.pred, args.gt)
    
    print(f"\n💡 说明:")
    print("APoc只计算遮挡关键点(visibility=1)的精度")
    print("这与标准COCO mAP不同，后者计算所有可见关键点")
    print(f"您之前看到的64.6% mAP是所有关键点的精度")
    print(f"而APoc {apoc:.1f}%是专门针对遮挡场景的指标")
    
    return 0

if __name__ == '__main__':
    import os
    main()
