# MPII数据集测试说明

## 概述
本文档说明如何使用PCT模型在MPII数据集上进行精度测试。

## 数据集结构
确保您的MPII数据集按以下结构组织：
```
data/MPII/mpii_human_pose_v1/
├── annotations/
│   ├── valid.json          # 验证集标注文件
│   ├── gt_valid.mat        # 评估用的ground truth文件
│   └── mpii_gt_val.mat     # 评估所需文件（从gt_valid.mat复制）
└── images/                 # 图像文件夹
```

## 配置文件
主要配置文件：
- `configs/mpii.py` - MPII数据集的基础配置（16个关键点定义）
- `configs/pct_mpii_test.py` - PCT模型在MPII数据集上的测试配置

### 关键配置说明
1. **关键点数量**：MPII数据集有16个关键点，而COCO有17个
2. **模型权重**：使用`work_dirs/pct_base_classifier/best_AP_epoch_282.pth`
3. **Tokenizer权重**：设置为空字符串以避免尺寸不匹配问题

## 运行测试

### 方法1：使用测试脚本（推荐）
```bash
python test_mpii.py
```

可选参数：
- `--config`: 配置文件路径（默认：configs/pct_mpii_test.py）
- `--checkpoint`: 模型权重文件（默认：work_dirs/pct_base_classifier/best_AP_epoch_282.pth）
- `--out`: 输出结果文件（默认：work_dirs/pct_mpii_test/result_keypoints.json）
- `--eval`: 评估指标（默认：PCKh）
- `--work-dir`: 工作目录（默认：work_dirs/pct_mpii_test）

### 方法2：直接使用tools/test.py
```bash
python tools/test.py configs/pct_mpii_test.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --out work_dirs/pct_mpii_test/result_keypoints.json --eval PCKh --work-dir work_dirs/pct_mpii_test
```

## 测试结果

### 最新测试结果（PCKh指标）
- **整体PCKh**: 1.99%
- **PCKh@0.1**: 0.10%

### 各关键点精度
- **Ankle**: 5.34%
- **Elbow**: 0.10%
- **Head**: 0.72%
- **Hip**: 1.45%
- **Knee**: 0.42%
- **Shoulder**: 0.12%
- **Wrist**: 0.39%

## 注意事项

### 模型兼容性问题
由于训练模型是基于COCO数据集（17个关键点），而MPII只有16个关键点，存在以下问题：
1. **Tokenizer权重不兼容**：需要将tokenizer的ckpt设置为空字符串
2. **主模型权重部分不匹配**：系统会自动跳过不匹配的层

### 性能说明
当前结果显示精度较低，这可能是因为：
1. 模型是在COCO数据集上训练的，直接在MPII上测试存在域适应问题
2. 关键点定义和顺序不同
3. 未使用MPII专门训练的模型

### 改进建议
1. **重新训练**：使用MPII数据集重新训练模型
2. **微调**：在MPII数据集上对COCO预训练模型进行微调
3. **数据增强**：使用更多的数据增强技术
4. **关键点映射**：研究COCO和MPII关键点之间的最佳映射关系

## 文件说明
- `test_mpii.py`: 自动化测试脚本
- `configs/mpii.py`: MPII数据集配置
- `configs/pct_mpii_test.py`: 测试配置文件
- `work_dirs/pct_mpii_test/result_keypoints.json`: 详细测试结果

## 故障排除

### 常见问题
1. **缺少gt文件**：确保`mpii_gt_val.mat`文件存在
2. **权重不匹配**：检查tokenizer的ckpt设置
3. **路径错误**：确认数据集路径正确

### 解决方案
```bash
# 复制gt文件
copy "data\MPII\mpii_human_pose_v1\annotations\gt_valid.mat" "data\MPII\mpii_human_pose_v1\annotations\mpii_gt_val.mat"
```
