#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCHuman训练环境检查脚本
"""

import os
import json
import sys
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (不存在)")
        return False

def check_directory_exists(dir_path, description):
    """检查目录是否存在"""
    if os.path.isdir(dir_path):
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} (不存在)")
        return False

def analyze_dataset(json_file):
    """分析数据集信息"""
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        num_images = len(data.get('images', []))
        num_annotations = len(data.get('annotations', []))
        
        # 分析关键点信息
        keypoint_counts = {}
        for ann in data.get('annotations', []):
            num_keypoints = ann.get('num_keypoints', 0)
            keypoint_counts[num_keypoints] = keypoint_counts.get(num_keypoints, 0) + 1
        
        print(f"  📊 图像数量: {num_images}")
        print(f"  📊 标注数量: {num_annotations}")
        print(f"  📊 关键点分布:")
        for num_kpts, count in sorted(keypoint_counts.items()):
            print(f"    - {num_kpts}个关键点: {count}个标注")
        
        return True
    except Exception as e:
        print(f"  ❌ 解析失败: {e}")
        return False

def check_image_samples(img_dir, json_file, max_check=5):
    """检查图像文件是否存在"""
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        images = data.get('images', [])[:max_check]
        missing_count = 0
        
        for img_info in images:
            img_path = os.path.join(img_dir, img_info['file_name'])
            if os.path.exists(img_path):
                print(f"  ✅ {img_info['file_name']}")
            else:
                print(f"  ❌ {img_info['file_name']} (不存在)")
                missing_count += 1
        
        if missing_count == 0:
            print(f"  ✅ 前{len(images)}张图像文件都存在")
        else:
            print(f"  ⚠️ 有{missing_count}张图像文件缺失")
        
        return missing_count == 0
    except Exception as e:
        print(f"  ❌ 检查图像失败: {e}")
        return False

def main():
    print("🔍 OCHuman训练环境检查")
    print("=" * 60)
    
    all_good = True
    
    # 1. 检查配置文件
    print("\n📁 1. 检查配置文件")
    config_files = [
        ('configs/pct_ochuman.py', 'OCHuman训练配置'),
        ('configs/coco.py', '基础COCO配置'),
    ]
    
    for file_path, desc in config_files:
        if not check_file_exists(file_path, desc):
            all_good = False
    
    # 2. 检查数据集
    print("\n📊 2. 检查OCHuman数据集")
    data_root = 'data/OCHuman'
    
    if not check_directory_exists(data_root, 'OCHuman数据集根目录'):
        all_good = False
    else:
        # 检查标注文件
        annotation_files = [
            ('ochuman_coco_format_test_range_0.00_1.00.json', '测试集标注'),
            ('ochuman_coco_format_val_range_0.00_1.00.json', '验证集标注'),
        ]
        
        for ann_file, desc in annotation_files:
            ann_path = os.path.join(data_root, ann_file)
            if check_file_exists(ann_path, desc):
                print(f"  📋 分析{desc}:")
                analyze_dataset(ann_path)
            else:
                all_good = False
        
        # 检查图像目录
        img_dir = os.path.join(data_root, 'images')
        if check_directory_exists(img_dir, '图像目录'):
            print(f"  🖼️ 检查图像文件样本:")
            test_json = os.path.join(data_root, 'ochuman_coco_format_test_range_0.00_1.00.json')
            if os.path.exists(test_json):
                check_image_samples(img_dir, test_json)
        else:
            all_good = False
    
    # 3. 检查预训练权重
    print("\n🏋️ 3. 检查预训练权重")
    weight_files = [
        ('weights/heatmap/swin_base.pth', 'SwinV2 backbone权重'),
        ('weights/tokenizer/test_codebook/best_AP_epoch_11.pth', 'Tokenizer权重'),
    ]
    
    for weight_path, desc in weight_files:
        if not check_file_exists(weight_path, desc):
            all_good = False
    
    # 4. 检查训练脚本
    print("\n🚀 4. 检查训练脚本")
    script_files = [
        ('tools/train.py', 'MMPose训练脚本'),
        ('tools/test.py', 'MMPose测试脚本'),
        ('train_ochuman.py', 'OCHuman训练启动脚本'),
    ]
    
    for script_path, desc in script_files:
        if not check_file_exists(script_path, desc):
            all_good = False
    
    # 5. 检查Python环境
    print("\n🐍 5. 检查Python环境")
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        print(f"✅ CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✅ CUDA版本: {torch.version.cuda}")
            print(f"✅ GPU数量: {torch.cuda.device_count()}")
    except ImportError:
        print("❌ PyTorch未安装")
        all_good = False
    
    try:
        import mmcv
        print(f"✅ MMCV版本: {mmcv.__version__}")
    except ImportError:
        print("❌ MMCV未安装")
        all_good = False
    
    try:
        import mmpose
        print(f"✅ MMPose版本: {mmpose.__version__}")
    except ImportError:
        print("❌ MMPose未安装")
        all_good = False
    
    # 总结
    print(f"\n{'='*60}")
    if all_good:
        print("🎉 所有检查通过！可以开始OCHuman训练了")
        print("\n🚀 启动训练命令:")
        print("python train_ochuman.py")
        print("\n📊 OCHuman训练配置:")
        print("- 模型: PCT (Pose Classification Transformer)")
        print("- 数据集: OCHuman (17个关键点)")
        print("- 总轮数: 120 epochs")
        print("- 验证频率: 每3个epoch")
        print("- 评估指标: mAP")
        print("- 批量大小: 32")
        print("- 学习率: 8e-4")
        print("- 工作目录: work_dirs/pct_ochuman")
        
        print("\n🔧 其他可用命令:")
        print("# 只训练")
        print("python train_ochuman.py --mode train")
        print()
        print("# 只测试")
        print("python train_ochuman.py --mode test --checkpoint work_dirs/pct_ochuman/best_AP_epoch_*.pth")
        print()
        print("# 从checkpoint恢复训练")
        print("python train_ochuman.py --resume-from work_dirs/pct_ochuman/epoch_60.pth")
        
    else:
        print("❌ 检查发现问题，请解决后再开始训练")
        print("\n🔧 可能的解决方案:")
        print("1. 确保OCHuman数据集已正确下载和解压")
        print("2. 确保预训练权重文件存在")
        print("3. 确保Python环境已安装必要的包")
        print("4. 检查文件路径是否正确")
    
    print(f"{'='*60}")
    
    return 0 if all_good else 1

if __name__ == '__main__':
    sys.exit(main())
