#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MPII数据集两阶段训练脚本
第一阶段：训练tokenizer
第二阶段：加载第一阶段的tokenizer权重，训练classifier
"""

import os
import sys
import argparse
import subprocess
import glob
import shutil

def check_prerequisites():
    """检查训练前提条件"""
    print("🔍 检查两阶段训练前提条件...")
    
    # 检查配置文件
    stage1_config = 'configs/pct_mpii_tokenizer_stage1.py'
    stage2_config = 'configs/pct_mpii_classifier_stage2.py'
    
    for config in [stage1_config, stage2_config]:
        if not os.path.exists(config):
            print(f"❌ 配置文件不存在: {config}")
            return False
        print(f"✅ 配置文件存在: {config}")
    
    # 检查MPII数据集
    data_root = 'data/MPII/mpii_human_pose_v1'
    required_files = [
        'annotations/train.json',
        'annotations/valid.json',
        'images'
    ]
    
    for file in required_files:
        path = os.path.join(data_root, file)
        if not os.path.exists(path):
            print(f"❌ MPII数据文件不存在: {path}")
            return False
        print(f"✅ MPII数据文件存在: {path}")
    
    # 检查backbone预训练权重
    backbone_weight = 'weights/heatmap/swin_base.pth'
    if not os.path.exists(backbone_weight):
        print(f"❌ Backbone预训练权重不存在: {backbone_weight}")
        return False
    print(f"✅ Backbone预训练权重存在: {backbone_weight}")
    
    return True

def run_stage1_training(config, work_dir, resume_from=None, seed=42):
    """运行第一阶段tokenizer训练"""
    cmd = [
        'python', 'tools/train.py',
        config,
        '--work-dir', work_dir,
        '--seed', str(seed)
    ]
    
    if resume_from:
        cmd.extend(['--resume-from', resume_from])
    
    print(f"\n{'='*60}")
    print("🚀 第一阶段：MPII Tokenizer训练")
    print(f"{'='*60}")
    print(f"配置文件: {config}")
    print(f"工作目录: {work_dir}")
    print(f"随机种子: {seed}")
    if resume_from:
        print(f"恢复训练: {resume_from}")
    print(f"执行命令: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ 第一阶段训练完成!")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 第一阶段训练失败，错误代码: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print(f"\n⚠️ 第一阶段训练被用户中断")
        return 1

def find_best_stage1_checkpoint(work_dir):
    """找到第一阶段最佳的checkpoint"""
    # 查找best文件
    pattern = os.path.join(work_dir, 'best_PCKh_epoch_*.pth')
    checkpoints = glob.glob(pattern)
    if checkpoints:
        return max(checkpoints, key=os.path.getmtime)
    
    # 如果没有best文件，找最新的epoch文件
    pattern = os.path.join(work_dir, 'epoch_*.pth')
    checkpoints = glob.glob(pattern)
    if checkpoints:
        return max(checkpoints, key=os.path.getmtime)
    
    return None

def update_stage2_config(config_path, tokenizer_ckpt_path):
    """更新第二阶段配置文件，设置tokenizer权重路径"""
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换tokenizer的ckpt路径
    content = content.replace(
        'ckpt="",  # 将在训练脚本中动态设置第一阶段的tokenizer权重',
        f'ckpt="{tokenizer_ckpt_path}",  # 第一阶段训练的tokenizer权重'
    )
    
    # 创建临时配置文件
    temp_config_path = config_path.replace('.py', '_temp.py')
    with open(temp_config_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return temp_config_path

def run_stage2_training(config, work_dir, tokenizer_ckpt, resume_from=None, seed=42):
    """运行第二阶段classifier训练"""
    
    # 更新配置文件
    temp_config = update_stage2_config(config, tokenizer_ckpt)
    
    cmd = [
        'python', 'tools/train.py',
        temp_config,
        '--work-dir', work_dir,
        '--seed', str(seed)
    ]
    
    if resume_from:
        cmd.extend(['--resume-from', resume_from])
    
    print(f"\n{'='*60}")
    print("🚀 第二阶段：MPII Classifier训练")
    print(f"{'='*60}")
    print(f"配置文件: {temp_config}")
    print(f"工作目录: {work_dir}")
    print(f"Tokenizer权重: {tokenizer_ckpt}")
    print(f"随机种子: {seed}")
    if resume_from:
        print(f"恢复训练: {resume_from}")
    print(f"执行命令: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ 第二阶段训练完成!")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 第二阶段训练失败，错误代码: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print(f"\n⚠️ 第二阶段训练被用户中断")
        return 1
    finally:
        # 清理临时配置文件
        if os.path.exists(temp_config):
            os.remove(temp_config)

def main():
    parser = argparse.ArgumentParser(description='MPII dataset two-stage training')
    parser.add_argument('--stage1-config', 
                       default='configs/pct_mpii_tokenizer_stage1.py',
                       help='第一阶段配置文件路径')
    parser.add_argument('--stage2-config',
                       default='configs/pct_mpii_classifier_stage2.py',
                       help='第二阶段配置文件路径')
    parser.add_argument('--stage1-work-dir', 
                       default='work_dirs/pct_mpii_tokenizer_stage1',
                       help='第一阶段工作目录')
    parser.add_argument('--stage2-work-dir',
                       default='work_dirs/pct_mpii_classifier_stage2',
                       help='第二阶段工作目录')
    parser.add_argument('--resume-stage1', 
                       default=None,
                       help='第一阶段恢复训练的checkpoint')
    parser.add_argument('--resume-stage2',
                       default=None,
                       help='第二阶段恢复训练的checkpoint')
    parser.add_argument('--skip-stage1',
                       action='store_true',
                       help='跳过第一阶段，直接进行第二阶段')
    parser.add_argument('--tokenizer-ckpt',
                       default=None,
                       help='指定tokenizer权重路径（当跳过第一阶段时使用）')
    parser.add_argument('--seed', 
                       type=int, 
                       default=42,
                       help='随机种子')
    
    args = parser.parse_args()
    
    print("🎯 PCT两阶段训练 - MPII数据集")
    print("=" * 60)
    
    # 检查前提条件
    if not check_prerequisites():
        print("\n❌ 前提条件检查失败，请确保所有必要文件存在")
        return 1
    
    print("\n✅ 所有前提条件检查通过!")
    
    # 创建工作目录
    os.makedirs(args.stage1_work_dir, exist_ok=True)
    os.makedirs(args.stage2_work_dir, exist_ok=True)
    
    print("\n📋 两阶段训练说明:")
    print("第一阶段 (Tokenizer):")
    print("- 目标: 学习关键点的token表示")
    print("- 数据集: MPII (16个关键点)")
    print("- 训练轮数: 120 epochs")
    print("- 评估指标: PCKh@0.5")
    print("- 验证频率: 每3个epoch")
    
    print("\n第二阶段 (Classifier):")
    print("- 目标: 学习从图像到token的映射")
    print("- 基础: 第一阶段训练的tokenizer")
    print("- 训练轮数: 120 epochs")
    print("- 评估指标: PCKh@0.5")
    print("- 验证频率: 每3个epoch")
    print("- Tokenizer: 冻结参数，只训练classifier")
    
    # 第一阶段训练
    if not args.skip_stage1:
        result = run_stage1_training(
            config=args.stage1_config,
            work_dir=args.stage1_work_dir,
            resume_from=args.resume_stage1,
            seed=args.seed
        )
        
        if result != 0:
            print("❌ 第一阶段训练失败")
            return result
        
        # 找到第一阶段最佳checkpoint
        tokenizer_ckpt = find_best_stage1_checkpoint(args.stage1_work_dir)
        if not tokenizer_ckpt:
            print("❌ 未找到第一阶段训练的checkpoint")
            return 1
    else:
        if not args.tokenizer_ckpt:
            print("❌ 跳过第一阶段时必须指定--tokenizer-ckpt")
            return 1
        tokenizer_ckpt = args.tokenizer_ckpt
        if not os.path.exists(tokenizer_ckpt):
            print(f"❌ 指定的tokenizer权重不存在: {tokenizer_ckpt}")
            return 1
    
    print(f"\n🔗 使用Tokenizer权重: {tokenizer_ckpt}")
    
    # 第二阶段训练
    result = run_stage2_training(
        config=args.stage2_config,
        work_dir=args.stage2_work_dir,
        tokenizer_ckpt=tokenizer_ckpt,
        resume_from=args.resume_stage2,
        seed=args.seed
    )
    
    if result != 0:
        print("❌ 第二阶段训练失败")
        return result
    
    # 训练完成总结
    print(f"\n{'='*60}")
    print("🎉 两阶段训练完成!")
    print(f"{'='*60}")
    
    stage2_best = find_best_stage1_checkpoint(args.stage2_work_dir)
    if stage2_best:
        print(f"✅ 最终模型权重: {stage2_best}")
        
        print(f"\n🧪 测试命令:")
        print(f"python tools/test.py {args.stage2_config} {stage2_best} --eval PCKh")
        
        print(f"\n📊 训练结果:")
        print(f"- 第一阶段工作目录: {args.stage1_work_dir}")
        print(f"- 第二阶段工作目录: {args.stage2_work_dir}")
        print(f"- Tokenizer权重: {tokenizer_ckpt}")
        print(f"- 最终模型: {stage2_best}")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
