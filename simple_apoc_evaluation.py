#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的OCHuman APoc评估
基于PCT输出格式
"""

import json
import numpy as np
import os

def compute_pck(pred_kpts, gt_kpts, gt_vis, threshold=0.5):
    """计算PCK (Percentage of Correct Keypoints)"""
    if len(pred_kpts) != len(gt_kpts):
        return 0.0
    
    pred_kpts = np.array(pred_kpts).reshape(-1, 3)  # [x, y, score]
    gt_kpts = np.array(gt_kpts).reshape(-1, 3)      # [x, y, vis]
    
    # 只考虑遮挡但可见的关键点 (visibility=1)
    occluded_mask = (gt_kpts[:, 2] == 1)
    
    if not np.any(occluded_mask):
        return None  # 没有遮挡关键点
    
    pred_occluded = pred_kpts[occluded_mask]
    gt_occluded = gt_kpts[occluded_mask]
    
    # 计算距离
    distances = np.sqrt(np.sum((pred_occluded[:, :2] - gt_occluded[:, :2])**2, axis=1))
    
    # 使用头部尺寸作为归一化因子 (简化版本，使用固定阈值)
    head_size = 60  # 假设头部尺寸为60像素
    normalized_distances = distances / head_size
    
    # 计算正确的关键点比例
    correct = normalized_distances < threshold
    pck = np.sum(correct) / len(correct)
    
    return pck

def evaluate_simple_apoc():
    """简化的APoc评估"""
    
    # 加载预测结果
    pred_file = 'work_dirs/coco_on_ochuman_direct_test.json'
    gt_file = 'data/OCHuman/ochuman_coco_format_val_range_0.00_1.00.json'
    
    with open(pred_file, 'r') as f:
        pred_data = json.load(f)
    
    with open(gt_file, 'r') as f:
        gt_data = json.load(f)
    
    print("🔍 简化OCHuman APoc评估")
    print("=" * 50)
    
    # 构建图像ID到文件名的映射
    img_id_to_filename = {}
    for img in gt_data['images']:
        img_id_to_filename[img['id']] = img['file_name']
    
    # 构建文件名到GT的映射
    filename_to_gt = {}
    for ann in gt_data['annotations']:
        img_id = ann['image_id']
        filename = img_id_to_filename[img_id]
        if filename not in filename_to_gt:
            filename_to_gt[filename] = []
        filename_to_gt[filename].append(ann)
    
    # 处理预测结果 (是一个列表)
    all_predictions = []
    all_image_paths = []

    for item in pred_data:
        preds = item['preds']
        paths = item['image_paths']
        for pred, path in zip(preds, paths):
            all_predictions.append(pred)
            all_image_paths.append(path)

    print(f"预测数量: {len(all_predictions)}")
    print(f"图像路径数量: {len(all_image_paths)}")

    # 评估每个预测
    pck_scores = []
    total_occluded_samples = 0
    valid_predictions = 0

    for i, (img_path, pred_kpts) in enumerate(zip(all_image_paths, all_predictions)):
        # 提取文件名
        filename = os.path.basename(img_path)
        
        if filename not in filename_to_gt:
            continue
        
        # 找到对应的GT标注
        gt_anns = filename_to_gt[filename]
        
        # 简化：使用第一个GT标注 (实际应该根据bbox匹配)
        if len(gt_anns) > 0:
            gt_ann = gt_anns[0]  # 简化处理
            gt_kpts = gt_ann['keypoints']
            
            # 检查是否有遮挡关键点
            gt_vis = np.array(gt_kpts)[2::3]
            occluded_mask = (gt_vis == 1)
            
            if np.any(occluded_mask):
                pck = compute_pck(pred_kpts, gt_kpts, gt_vis)
                if pck is not None:
                    pck_scores.append(pck)
                    valid_predictions += 1
                total_occluded_samples += 1
    
    # 计算平均APoc (简化为PCK)
    if len(pck_scores) > 0:
        apoc_simplified = np.mean(pck_scores)
        print(f"\n📊 简化APoc结果:")
        print(f"APoc (简化PCK): {apoc_simplified:.3f} ({apoc_simplified*100:.1f}%)")
        print(f"有效预测数: {valid_predictions}")
        print(f"遮挡样本数: {total_occluded_samples}")
        
        # 按阈值分析
        thresholds = [0.1, 0.2, 0.3, 0.4, 0.5]
        print(f"\n📈 不同阈值下的PCK:")
        for thr in thresholds:
            pck_at_thr = []
            for i, (img_path, pred_kpts) in enumerate(zip(all_image_paths, all_predictions)):
                filename = os.path.basename(img_path)
                if filename not in filename_to_gt:
                    continue
                
                gt_anns = filename_to_gt[filename]
                if len(gt_anns) > 0:
                    gt_ann = gt_anns[0]
                    gt_kpts = gt_ann['keypoints']
                    gt_vis = np.array(gt_kpts)[2::3]
                    occluded_mask = (gt_vis == 1)
                    
                    if np.any(occluded_mask):
                        pck = compute_pck(pred_kpts, gt_kpts, gt_vis, threshold=thr)
                        if pck is not None:
                            pck_at_thr.append(pck)
            
            if len(pck_at_thr) > 0:
                avg_pck = np.mean(pck_at_thr)
                print(f"  PCK@{thr}: {avg_pck:.3f} ({avg_pck*100:.1f}%)")
    else:
        print("❌ 没有找到有效的遮挡样本进行评估")
    
    print(f"\n💡 说明:")
    print("1. 这是简化的APoc评估，使用PCK代替OKS")
    print("2. 只计算遮挡关键点(visibility=1)的精度")
    print("3. 您之前的64.6% mAP是所有关键点的COCO指标")
    print("4. APoc专门针对遮挡场景，通常会比整体mAP低")
    
    # 与论文结果对比
    print(f"\n📚 论文中的典型APoc结果:")
    print("- HRNet: ~45-55%")
    print("- SimpleBaseline: ~40-50%") 
    print("- 顶级方法: ~60-70%")
    
    if len(pck_scores) > 0:
        apoc_pct = apoc_simplified * 100
        if apoc_pct > 50:
            print(f"✅ 您的结果 {apoc_pct:.1f}% 表现不错!")
        elif apoc_pct > 30:
            print(f"⚠️ 您的结果 {apoc_pct:.1f}% 中等，有改进空间")
        else:
            print(f"❌ 您的结果 {apoc_pct:.1f}% 较低，需要优化")

if __name__ == '__main__':
    evaluate_simple_apoc()
