#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析COCO和MPII数据集的坐标量级差异
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import os

def analyze_mpii_coordinates():
    """分析MPII数据集的坐标分布"""
    print("🔍 分析MPII数据集坐标分布...")
    
    mpii_file = 'data/MPII/mpii_human_pose_v1/annotations/train.json'
    if not os.path.exists(mpii_file):
        print(f"❌ MPII数据文件不存在: {mpii_file}")
        return None
    
    with open(mpii_file, 'r') as f:
        mpii_data = json.load(f)
    
    all_coords = []
    all_x = []
    all_y = []
    
    # 收集所有坐标点
    annotations = mpii_data if isinstance(mpii_data, list) else mpii_data.get('annotations', [])
    for ann in annotations[:1000]:  # 只分析前1000个样本
        if 'joints' in ann:
            joints = ann['joints']  # MPII格式是[[x,y], [x,y], ...]
            joints_vis = ann.get('joints_vis', [1] * len(joints))
            for i, (joint, vis) in enumerate(zip(joints, joints_vis)):
                if vis > 0 and len(joint) >= 2:  # 只统计可见的关键点
                    x, y = joint[0], joint[1]
                    if x > 0 and y > 0:  # 只统计有效坐标
                        all_coords.append([x, y])
                        all_x.append(x)
                        all_y.append(y)
    
    if not all_coords:
        print("❌ 没有找到有效的MPII坐标数据")
        return None
    
    coords_array = np.array(all_coords)
    
    stats = {
        'dataset': 'MPII',
        'total_points': len(all_coords),
        'x_min': np.min(all_x),
        'x_max': np.max(all_x),
        'x_mean': np.mean(all_x),
        'x_std': np.std(all_x),
        'y_min': np.min(all_y),
        'y_max': np.max(all_y),
        'y_mean': np.mean(all_y),
        'y_std': np.std(all_y),
        'coord_magnitude': np.mean(np.sqrt(np.sum(coords_array**2, axis=1)))
    }
    
    return stats, all_x, all_y

def analyze_coco_coordinates():
    """分析COCO数据集的坐标分布"""
    print("🔍 分析COCO数据集坐标分布...")
    
    coco_file = 'data/coco/annotations/person_keypoints_train2017.json'
    if not os.path.exists(coco_file):
        print(f"❌ COCO数据文件不存在: {coco_file}")
        return None
    
    with open(coco_file, 'r') as f:
        coco_data = json.load(f)
    
    all_coords = []
    all_x = []
    all_y = []
    
    # 收集所有坐标点
    for ann in coco_data.get('annotations', [])[:1000]:  # 只分析前1000个样本
        if 'keypoints' in ann:
            keypoints = ann['keypoints']
            for i in range(0, len(keypoints), 3):  # x, y, visibility
                if i+1 < len(keypoints):
                    x, y = keypoints[i], keypoints[i+1]
                    if x > 0 and y > 0:  # 只统计有效坐标
                        all_coords.append([x, y])
                        all_x.append(x)
                        all_y.append(y)
    
    if not all_coords:
        print("❌ 没有找到有效的COCO坐标数据")
        return None
    
    coords_array = np.array(all_coords)
    
    stats = {
        'dataset': 'COCO',
        'total_points': len(all_coords),
        'x_min': np.min(all_x),
        'x_max': np.max(all_x),
        'x_mean': np.mean(all_x),
        'x_std': np.std(all_x),
        'y_min': np.min(all_y),
        'y_max': np.max(all_y),
        'y_mean': np.mean(all_y),
        'y_std': np.std(all_y),
        'coord_magnitude': np.mean(np.sqrt(np.sum(coords_array**2, axis=1)))
    }
    
    return stats, all_x, all_y

def print_comparison(mpii_stats, coco_stats):
    """打印对比结果"""
    print("\n" + "="*80)
    print("📊 COCO vs MPII 坐标量级对比分析")
    print("="*80)
    
    if mpii_stats:
        print(f"\n🎯 MPII数据集统计:")
        print(f"   总坐标点数: {mpii_stats['total_points']:,}")
        print(f"   X坐标范围: [{mpii_stats['x_min']:.1f}, {mpii_stats['x_max']:.1f}]")
        print(f"   X坐标均值: {mpii_stats['x_mean']:.1f} ± {mpii_stats['x_std']:.1f}")
        print(f"   Y坐标范围: [{mpii_stats['y_min']:.1f}, {mpii_stats['y_max']:.1f}]")
        print(f"   Y坐标均值: {mpii_stats['y_mean']:.1f} ± {mpii_stats['y_std']:.1f}")
        print(f"   坐标量级: {mpii_stats['coord_magnitude']:.1f}")
    
    if coco_stats:
        print(f"\n🎯 COCO数据集统计:")
        print(f"   总坐标点数: {coco_stats['total_points']:,}")
        print(f"   X坐标范围: [{coco_stats['x_min']:.1f}, {coco_stats['x_max']:.1f}]")
        print(f"   X坐标均值: {coco_stats['x_mean']:.1f} ± {coco_stats['x_std']:.1f}")
        print(f"   Y坐标范围: [{coco_stats['y_min']:.1f}, {coco_stats['y_max']:.1f}]")
        print(f"   Y坐标均值: {coco_stats['y_mean']:.1f} ± {coco_stats['y_std']:.1f}")
        print(f"   坐标量级: {coco_stats['coord_magnitude']:.1f}")
    
    if mpii_stats and coco_stats:
        print(f"\n⚖️ 量级差异分析:")
        x_ratio = mpii_stats['x_mean'] / coco_stats['x_mean']
        y_ratio = mpii_stats['y_mean'] / coco_stats['y_mean']
        mag_ratio = mpii_stats['coord_magnitude'] / coco_stats['coord_magnitude']
        
        print(f"   X坐标均值比例: {x_ratio:.2f}x")
        print(f"   Y坐标均值比例: {y_ratio:.2f}x")
        print(f"   整体量级比例: {mag_ratio:.2f}x")
        
        print(f"\n💡 损失影响分析:")
        l2_ratio = mag_ratio ** 2
        print(f"   L2损失预期比例: {l2_ratio:.2f}x (因为L2损失与坐标平方成正比)")
        
        if l2_ratio > 10:
            print(f"   ⚠️ 损失差异巨大！建议进行坐标归一化")
        elif l2_ratio > 2:
            print(f"   ⚠️ 损失差异较大，可能需要调整损失权重")
        else:
            print(f"   ✅ 损失差异在合理范围内")

def main():
    print("🔍 开始分析COCO和MPII数据集的坐标量级差异...")
    
    # 分析MPII
    mpii_result = analyze_mpii_coordinates()
    mpii_stats = mpii_result[0] if mpii_result else None
    
    # 分析COCO
    coco_result = analyze_coco_coordinates()
    coco_stats = coco_result[0] if coco_result else None
    
    # 打印对比结果
    print_comparison(mpii_stats, coco_stats)
    
    # 如果有数据，生成可视化
    if mpii_result and coco_result:
        try:
            plt.figure(figsize=(12, 5))
            
            plt.subplot(1, 2, 1)
            plt.scatter(mpii_result[1][:1000], mpii_result[2][:1000], alpha=0.5, s=1, label='MPII')
            plt.xlabel('X坐标')
            plt.ylabel('Y坐标')
            plt.title('MPII坐标分布')
            plt.legend()
            
            plt.subplot(1, 2, 2)
            plt.scatter(coco_result[1][:1000], coco_result[2][:1000], alpha=0.5, s=1, label='COCO')
            plt.xlabel('X坐标')
            plt.ylabel('Y坐标')
            plt.title('COCO坐标分布')
            plt.legend()
            
            plt.tight_layout()
            plt.savefig('coordinate_comparison.png', dpi=150, bbox_inches='tight')
            print(f"\n📊 坐标分布可视化已保存到: coordinate_comparison.png")
        except Exception as e:
            print(f"⚠️ 可视化生成失败: {e}")

if __name__ == '__main__':
    main()
