dataset_info = dict(
    dataset_name='mpii',
    paper_info=dict(
        author='<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> and '
        '<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>',
        title='2d human pose estimation: New benchmark and '
        'state of the art analysis',
        container='IEEE Conference on Computer Vision and '
        'Pattern Recognition (CVPR)',
        year='2014',
        homepage='http://human-pose.mpi-inf.mpg.de/',
    ),
    keypoint_info={
        0:
        dict(
            name='right_ankle',
            id=0,
            color=[255, 128, 0],
            type='lower',
            swap='left_ankle'),
        1:
        dict(
            name='right_knee',
            id=1,
            color=[255, 128, 0],
            type='lower',
            swap='left_knee'),
        2:
        dict(
            name='right_hip',
            id=2,
            color=[255, 128, 0],
            type='lower',
            swap='left_hip'),
        3:
        dict(
            name='left_hip',
            id=3,
            color=[0, 255, 0],
            type='lower',
            swap='right_hip'),
        4:
        dict(
            name='left_knee',
            id=4,
            color=[0, 255, 0],
            type='lower',
            swap='right_knee'),
        5:
        dict(
            name='left_ankle',
            id=5,
            color=[0, 255, 0],
            type='lower',
            swap='right_ankle'),
        6:
        dict(
            name='pelvis',
            id=6,
            color=[51, 153, 255],
            type='lower',
            swap=''),
        7:
        dict(
            name='thorax',
            id=7,
            color=[51, 153, 255],
            type='upper',
            swap=''),
        8:
        dict(
            name='upper_neck',
            id=8,
            color=[51, 153, 255],
            type='upper',
            swap=''),
        9:
        dict(
            name='head_top',
            id=9,
            color=[51, 153, 255],
            type='upper',
            swap=''),
        10:
        dict(
            name='right_wrist',
            id=10,
            color=[255, 128, 0],
            type='upper',
            swap='left_wrist'),
        11:
        dict(
            name='right_elbow',
            id=11,
            color=[255, 128, 0],
            type='upper',
            swap='left_elbow'),
        12:
        dict(
            name='right_shoulder',
            id=12,
            color=[255, 128, 0],
            type='upper',
            swap='left_shoulder'),
        13:
        dict(
            name='left_shoulder',
            id=13,
            color=[0, 255, 0],
            type='upper',
            swap='right_shoulder'),
        14:
        dict(
            name='left_elbow',
            id=14,
            color=[0, 255, 0],
            type='upper',
            swap='right_elbow'),
        15:
        dict(
            name='left_wrist',
            id=15,
            color=[0, 255, 0],
            type='upper',
            swap='right_wrist')
    },
    skeleton_info={
        0:
        dict(link=('right_ankle', 'right_knee'), id=0, color=[255, 128, 0]),
        1:
        dict(link=('right_knee', 'right_hip'), id=1, color=[255, 128, 0]),
        2:
        dict(link=('left_ankle', 'left_knee'), id=2, color=[0, 255, 0]),
        3:
        dict(link=('left_knee', 'left_hip'), id=3, color=[0, 255, 0]),
        4:
        dict(link=('right_hip', 'pelvis'), id=4, color=[255, 128, 0]),
        5:
        dict(link=('left_hip', 'pelvis'), id=5, color=[0, 255, 0]),
        6:
        dict(link=('pelvis', 'thorax'), id=6, color=[51, 153, 255]),
        7:
        dict(link=('thorax', 'upper_neck'), id=7, color=[51, 153, 255]),
        8:
        dict(link=('upper_neck', 'head_top'), id=8, color=[51, 153, 255]),
        9:
        dict(link=('right_wrist', 'right_elbow'), id=9, color=[255, 128, 0]),
        10:
        dict(link=('right_elbow', 'right_shoulder'), id=10, color=[255, 128, 0]),
        11:
        dict(link=('left_wrist', 'left_elbow'), id=11, color=[0, 255, 0]),
        12:
        dict(link=('left_elbow', 'left_shoulder'), id=12, color=[0, 255, 0]),
        13:
        dict(link=('right_shoulder', 'thorax'), id=13, color=[255, 128, 0]),
        14:
        dict(link=('left_shoulder', 'thorax'), id=14, color=[0, 255, 0])
    },
    joint_weights=[
        1.5, 1.2, 1., 1., 1.2, 1.5, 1., 1., 1., 1., 1.5, 1.2, 1., 1., 1.2, 1.5
    ],
    sigmas=[
        0.089, 0.087, 0.107, 0.107, 0.087, 0.089, 0.062, 0.072, 0.079, 0.072,
        0.062, 0.072, 0.079, 0.079, 0.072, 0.062
    ])
