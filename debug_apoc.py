#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试APoc评估问题
"""

import json
import numpy as np
import os

def debug_predictions():
    """调试预测结果"""
    
    # 加载数据
    with open('work_dirs/coco_on_ochuman_direct_test.json', 'r') as f:
        pred_data = json.load(f)
    
    with open('data/OCHuman/ochuman_coco_format_val_range_0.00_1.00.json', 'r') as f:
        gt_data = json.load(f)
    
    print("🔍 调试APoc评估问题")
    print("=" * 50)
    
    # 构建映射
    img_id_to_filename = {}
    for img in gt_data['images']:
        img_id_to_filename[img['id']] = img['file_name']
    
    filename_to_gt = {}
    for ann in gt_data['annotations']:
        img_id = ann['image_id']
        filename = img_id_to_filename[img_id]
        if filename not in filename_to_gt:
            filename_to_gt[filename] = []
        filename_to_gt[filename].append(ann)
    
    # 处理预测结果
    all_predictions = []
    all_image_paths = []
    
    for item in pred_data:
        preds = item['preds']
        paths = item['image_paths']
        for pred, path in zip(preds, paths):
            all_predictions.append(pred)
            all_image_paths.append(path)
    
    print(f"总预测数: {len(all_predictions)}")
    
    # 分析前几个样本
    for i in range(min(3, len(all_predictions))):
        img_path = all_image_paths[i]
        pred_kpts = all_predictions[i]
        filename = os.path.basename(img_path)
        
        print(f"\n样本 {i+1}: {filename}")
        print(f"预测关键点数: {len(pred_kpts)}")
        
        if len(pred_kpts) > 0:
            pred_array = np.array(pred_kpts)
            print(f"预测坐标范围: x=[{pred_array[:, 0].min():.1f}, {pred_array[:, 0].max():.1f}], y=[{pred_array[:, 1].min():.1f}, {pred_array[:, 1].max():.1f}]")
            print(f"预测分数范围: [{pred_array[:, 2].min():.3f}, {pred_array[:, 2].max():.3f}]")
        
        if filename in filename_to_gt:
            gt_ann = filename_to_gt[filename][0]
            gt_kpts = np.array(gt_ann['keypoints']).reshape(-1, 3)
            gt_vis = gt_kpts[:, 2]
            
            print(f"GT关键点数: {len(gt_kpts)}")
            print(f"GT坐标范围: x=[{gt_kpts[:, 0].min():.1f}, {gt_kpts[:, 0].max():.1f}], y=[{gt_kpts[:, 1].min():.1f}, {gt_kpts[:, 1].max():.1f}]")
            print(f"GT可见性统计: {dict(zip(*np.unique(gt_vis, return_counts=True)))}")
            
            # 检查遮挡关键点
            occluded_mask = (gt_vis == 1)
            if np.any(occluded_mask):
                occluded_gt = gt_kpts[occluded_mask]
                occluded_pred = np.array(pred_kpts)[occluded_mask] if len(pred_kpts) == len(gt_kpts) else None
                
                print(f"遮挡关键点数: {np.sum(occluded_mask)}")
                print(f"遮挡GT坐标: {occluded_gt[:, :2].tolist()}")
                if occluded_pred is not None:
                    print(f"遮挡预测坐标: {occluded_pred[:, :2].tolist()}")
                    
                    # 计算距离
                    distances = np.sqrt(np.sum((occluded_pred[:, :2] - occluded_gt[:, :2])**2, axis=1))
                    print(f"距离: {distances.tolist()}")
                    print(f"平均距离: {np.mean(distances):.1f} 像素")
                else:
                    print("预测关键点数量不匹配")
            else:
                print("没有遮挡关键点")
        else:
            print("未找到对应的GT")
    
    # 统计整体情况
    print(f"\n📊 整体统计:")
    
    matched_samples = 0
    total_occluded_kpts = 0
    distance_stats = []
    
    for i, (img_path, pred_kpts) in enumerate(zip(all_image_paths, all_predictions)):
        filename = os.path.basename(img_path)
        
        if filename in filename_to_gt and len(pred_kpts) == 17:
            gt_ann = filename_to_gt[filename][0]
            gt_kpts = np.array(gt_ann['keypoints']).reshape(-1, 3)
            gt_vis = gt_kpts[:, 2]
            
            occluded_mask = (gt_vis == 1)
            if np.any(occluded_mask):
                matched_samples += 1
                total_occluded_kpts += np.sum(occluded_mask)
                
                occluded_gt = gt_kpts[occluded_mask]
                occluded_pred = np.array(pred_kpts)[occluded_mask]
                
                distances = np.sqrt(np.sum((occluded_pred[:, :2] - occluded_gt[:, :2])**2, axis=1))
                distance_stats.extend(distances.tolist())
    
    print(f"匹配样本数: {matched_samples}")
    print(f"总遮挡关键点数: {total_occluded_kpts}")
    
    if len(distance_stats) > 0:
        distance_stats = np.array(distance_stats)
        print(f"距离统计:")
        print(f"  平均距离: {np.mean(distance_stats):.1f} 像素")
        print(f"  中位数距离: {np.median(distance_stats):.1f} 像素")
        print(f"  最小距离: {np.min(distance_stats):.1f} 像素")
        print(f"  最大距离: {np.max(distance_stats):.1f} 像素")
        
        # 不同阈值下的准确率
        thresholds = [10, 20, 30, 50, 100]
        print(f"不同像素阈值下的准确率:")
        for thr in thresholds:
            acc = np.sum(distance_stats < thr) / len(distance_stats)
            print(f"  <{thr}px: {acc:.3f} ({acc*100:.1f}%)")

if __name__ == '__main__':
    debug_predictions()
